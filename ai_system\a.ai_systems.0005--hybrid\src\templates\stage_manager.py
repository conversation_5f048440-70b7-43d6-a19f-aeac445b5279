#!/usr/bin/env python3

"""
Stage-Based Template Management System

This script demonstrates the new stage-based organization for templates:
- Stage 1 (1000-1999): Prototyping/Testing with auto-generated IDs
- Stage 2 (2000-2999): Validated but unplaced templates  
- Stage 3 (3000-3999): Finalized production templates
- Stage 4-9 (4000-9999): Reserved for future expansion

Key Features:
- Automatic ID generation for Stage 1 prototyping
- Stage distribution analysis
- Template migration between stages
- Compliance validation
"""

import os
import sys
import json
from lvl1_md_to_json import (
    TemplateConfig, 
    regenerate_catalog, 
    print_stage_distribution,
    get_next_stage1_id,
    validate_stage_compliance
)

class StageManager:
    """Manages stage-based template organization."""
    
    def __init__(self):
        self.catalog = None
        self.load_catalog()
    
    def load_catalog(self):
        """Load the current template catalog."""
        try:
            self.catalog = regenerate_catalog(force=False)
        except Exception as e:
            print(f"Error loading catalog: {e}")
            sys.exit(1)
    
    def show_stage_overview(self):
        """Display comprehensive stage overview."""
        print("=" * 60)
        print("STAGE-BASED TEMPLATE ORGANIZATION SYSTEM")
        print("=" * 60)
        
        print("\nSTAGE DEFINITIONS:")
        for stage_name, stage_info in TemplateConfig.STAGES.items():
            start, end = stage_info["range"]
            auto_id = "✓" if stage_info["auto_id"] else "✗"
            print(f"  {stage_name.upper()}: {start:4d}-{end:4d} | {stage_info['description']} | Auto-ID: {auto_id}")
        
        print_stage_distribution(self.catalog)
    
    def suggest_next_prototype_id(self):
        """Suggest next available ID for prototyping."""
        next_id = get_next_stage1_id(self.catalog)
        if next_id:
            print(f"\n🔧 NEXT PROTOTYPE ID: {next_id}")
            print(f"   Suggested format: {next_id}-a-your_template_name.md")
            print(f"   Category examples:")
            print(f"     {next_id}-a-content_analyzer.md")
            print(f"     {next_id}-a-text_transformer.md") 
            print(f"     {next_id}-a-pattern_extractor.md")
        else:
            print("\n⚠️  No available IDs in Stage1 range (1000-1999)")
    
    def validate_system_compliance(self):
        """Check system-wide stage compliance."""
        print("\n🔍 STAGE COMPLIANCE VALIDATION:")
        issues = validate_stage_compliance(self.catalog)
        
        if not issues:
            print("   ✅ All templates are stage-compliant")
        else:
            print(f"   ⚠️  Found {len(issues)} compliance issues:")
            for issue in issues:
                print(f"      - {issue}")
    
    def show_migration_suggestions(self):
        """Suggest templates that could be migrated between stages."""
        print("\n📋 MIGRATION SUGGESTIONS:")
        
        templates = self.catalog.get("templates", {})
        suggestions = []
        
        # Find legacy templates that could be moved to proper stages
        for template_id in templates.keys():
            stage_name, _ = TemplateConfig.get_stage_for_id(template_id)
            
            if stage_name == "legacy":
                if template_id.startswith(('0', '5', '6', '7', '8', '9')):
                    suggestions.append(f"   📦 {template_id} → Consider moving to appropriate stage")
        
        # Find stage1 templates that might be ready for stage2/3
        stage1_templates = [tid for tid in templates.keys() 
                           if TemplateConfig.get_stage_for_id(tid)[0] == "stage1"]
        
        if len(stage1_templates) > 5:  # Arbitrary threshold
            suggestions.append(f"   🔄 {len(stage1_templates)} Stage1 templates → Review for promotion to Stage2/3")
        
        if suggestions:
            for suggestion in suggestions:
                print(suggestion)
        else:
            print("   ✅ No migration suggestions at this time")
    
    def demonstrate_workflow(self):
        """Demonstrate the complete stage-based workflow."""
        print("\n" + "=" * 60)
        print("STAGE-BASED WORKFLOW DEMONSTRATION")
        print("=" * 60)
        
        print("\n1️⃣  PROTOTYPING WORKFLOW (Stage 1):")
        print("   • Get next auto-ID for new template")
        print("   • Create template with auto-generated ID")
        print("   • Test and iterate in Stage 1 range")
        print("   • No manual ID management required")
        
        print("\n2️⃣  VALIDATION WORKFLOW (Stage 2):")
        print("   • Move proven templates from Stage 1")
        print("   • Templates are validated but not yet categorized")
        print("   • Manual ID assignment for organization")
        print("   • Prepare for final production placement")
        
        print("\n3️⃣  PRODUCTION WORKFLOW (Stage 3):")
        print("   • Finalized, production-ready templates")
        print("   • Stable IDs that won't change")
        print("   • Full compliance with RulesForAI.md")
        print("   • Ready for system integration")
        
        print("\n4️⃣  EXPANSION WORKFLOW (Stage 4-9):")
        print("   • Reserved for future system expansion")
        print("   • Domain-specific template collections")
        print("   • Specialized template categories")
        print("   • Advanced template sequences")

def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Stage-Based Template Management System")
    parser.add_argument("--overview", action="store_true", help="Show complete stage overview")
    parser.add_argument("--next-id", action="store_true", help="Show next available prototype ID")
    parser.add_argument("--validate", action="store_true", help="Validate stage compliance")
    parser.add_argument("--migrate", action="store_true", help="Show migration suggestions")
    parser.add_argument("--demo", action="store_true", help="Demonstrate complete workflow")
    parser.add_argument("--all", action="store_true", help="Run all analysis commands")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        args.all = True  # Default to showing everything
    
    manager = StageManager()
    
    if args.overview or args.all:
        manager.show_stage_overview()
    
    if args.next_id or args.all:
        manager.suggest_next_prototype_id()
    
    if args.validate or args.all:
        manager.validate_system_compliance()
    
    if args.migrate or args.all:
        manager.show_migration_suggestions()
    
    if args.demo or args.all:
        manager.demonstrate_workflow()

if __name__ == "__main__":
    main()
