  {
  "initial_prompt": "Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.",
  "sequence_id": "1000",
  "results": [
      {
      "instruction": "# Concept Extractor\n\nYour goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:\n\n`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`",
      "step": "a",
      "title": "Concept Extractor",
      "input": "Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"concepts\": {\n    \"Artificial Intelligence (AI)\": {\n      \"type\": \"transformative technology\",\n      \"core_ideas\": [\n        \"Simulates human cognitive functions\",\n        \"Supports decision-making processes\"\n      ],\n      \"key_components\": {\n        \"Machine Learning Algorithms\": {\n          \"type\": \"computational method\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Enables learning from data\"\n        },\n        \"Neural Networks\": {\n          \"type\": \"computational architecture\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Models human brain for pattern recognition\"\n        },\n        \"Natural Language Processing (NLP)\": {\n          \"type\": \"AI domain\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Processes and understands human language\"\n        },\n        \"Computer Vision Systems\": {\n          \"type\": \"AI domain\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Interprets and understands visual information\"\n        }\n      },\n      \"relationships\": {\n        \"Encompasses\": [\n          \"Machine Learning Algorithms\",\n          \"Neural Networks\",\n          \"Natural Language Processing\",\n          \"Computer Vision Systems\"\n        ],\n        \"Simulates\": [\n          \"Human cognitive functions\",\n          \"Decision-making processes\"\n        ]\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "a",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"concepts\": {\n    \"Artificial Intelligence (AI)\": {\n      \"type\": \"transformative technology\",\n      \"core_ideas\": [\n        \"Simulates human cognitive functions\",\n        \"Supports decision-making processes\"\n      ],\n      \"key_components\": {\n        \"Machine Learning Algorithms\": {\n          \"type\": \"computational method\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Enables learning from data\"\n        },\n        \"Neural Networks\": {\n          \"type\": \"computational architecture\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Models human brain for pattern recognition\"\n        },\n        \"Natural Language Processing (NLP)\": {\n          \"type\": \"AI domain\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Processes and understands human language\"\n        },\n        \"Computer Vision Systems\": {\n          \"type\": \"AI domain\",\n          \"relationship\": \"subcategory of AI\",\n          \"function\": \"Interprets and understands visual information\"\n        }\n      },\n      \"relationships\": {\n        \"Encompasses\": [\n          \"Machine Learning Algorithms\",\n          \"Neural Networks\",\n          \"Natural Language Processing\",\n          \"Computer Vision Systems\"\n        ],\n        \"Simulates\": [\n          \"Human cognitive functions\",\n          \"Decision-making processes\"\n        ]\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"structured conceptual analysis; hierarchical outline; taxonomy; informational summary\"\n}",
        }
      }
    },
      {
      "instruction": "# Text Simplifier\n\nYour goal is not to **summarize** the text, but to **simplify** its language and structure while preserving all key information and meaning. Execute as:\n\n`{role=comprehensive_text_simplifier; input=[text:str]; process=[identify_complex_language(), break_down_long_sentences(), replace_difficult_vocabulary(), clarify_abstract_concepts(), maintain_original_meaning(), ensure_accessibility()]; constraints=[preserve_all_information(), maintain_logical_flow(), use_simple_language()]; requirements=[clear_communication(), accessible_vocabulary(), simplified_structure()]; output={simplified_text:str}}`",
      "step": "a",
      "title": "Text Simplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"what_it_is\": \"structured conceptual analysis; hierarchical outline; taxonomy; informational summary\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"simplified_text\": \"Artificial intelligence is a powerful technology that uses tools like machine learning, neural networks, natural language processing, and computer vision. These tools help computers act like humans by allowing them to learn, understand language, see and recognize images, and make decisions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Concept Extractor\n\nYour goal is not to **describe** the content, but to **extract** the primary concepts and their main relationships. Execute as:\n\n`{role=focused_concept_extractor; input=[content:any]; process=[identify_primary_concepts(), extract_main_relationships(), categorize_concept_types()]; constraints=[focus_on_primary_concepts(), ignore_minor_details()]; requirements=[clear_concept_identification(), relationship_clarity()]; output={concepts:dict}}`",
      "step": "b",
      "title": "Concept Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"simplified_text\": \"Artificial intelligence is a powerful technology that uses tools like machine learning, neural networks, natural language processing, and computer vision. These tools help computers act like humans by allowing them to learn, understand language, see and recognize images, and make decisions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"concepts\": {\n    \"Artificial Intelligence\": {\n      \"type\": \"Technology\",\n      \"relationships\": {\n        \"encompasses\": [\n          \"Machine Learning\",\n          \"Neural Networks\",\n          \"Natural Language Processing\",\n          \"Computer Vision\"\n        ],\n        \"purpose\": [\n          \"Simulate Human Cognitive Functions\",\n          \"Simulate Decision-Making Processes\"\n        ]\n      }\n    },\n    \"Machine Learning\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Neural Networks\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Natural Language Processing\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Computer Vision\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Simulate Human Cognitive Functions\": {\n      \"type\": \"Purpose\",\n      \"relationships\": {\n        \"achieved_by\": \"Artificial Intelligence\"\n      }\n    },\n    \"Simulate Decision-Making Processes\": {\n      \"type\": \"Purpose\",\n      \"relationships\": {\n        \"achieved_by\": \"Artificial Intelligence\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:\n\n`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"concepts\": {\n    \"Artificial Intelligence\": {\n      \"type\": \"Technology\",\n      \"relationships\": {\n        \"encompasses\": [\n          \"Machine Learning\",\n          \"Neural Networks\",\n          \"Natural Language Processing\",\n          \"Computer Vision\"\n        ],\n        \"purpose\": [\n          \"Simulate Human Cognitive Functions\",\n          \"Simulate Decision-Making Processes\"\n        ]\n      }\n    },\n    \"Machine Learning\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Neural Networks\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Natural Language Processing\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Computer Vision\": {\n      \"type\": \"Method\",\n      \"relationships\": {\n        \"subset_of\": \"Artificial Intelligence\"\n      }\n    },\n    \"Simulate Human Cognitive Functions\": {\n      \"type\": \"Purpose\",\n      \"relationships\": {\n        \"achieved_by\": \"Artificial Intelligence\"\n      }\n    },\n    \"Simulate Decision-Making Processes\": {\n      \"type\": \"Purpose\",\n      \"relationships\": {\n        \"achieved_by\": \"Artificial Intelligence\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"concept map\"\n}",
        }
      }
    },
      {
      "instruction": "# Text Simplifier\n\nYour goal is not to **rewrite** the text, but to **simplify** its most complex elements while keeping the core message clear. Execute as:\n\n`{role=focused_text_simplifier; input=[text:str]; process=[identify_main_complexity_barriers(), simplify_key_concepts(), clarify_essential_points()]; constraints=[focus_on_major_simplifications(), preserve_core_meaning()]; requirements=[improved_clarity(), accessible_language()]; output={simplified_text:str}}`",
      "step": "b",
      "title": "Text Simplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"what_it_is\": \"concept map\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"simplified_text\": \"Artificial intelligence is a powerful technology that uses computer programs to do tasks like learning, recognizing patterns, understanding language, and seeing images. It tries to act like how humans think and make decisions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Concept Extractor\n\nYour goal is not to **explain** the content, but to **extract** the essential concepts. Execute as:\n\n`{role=essential_concept_extractor; input=[content:any]; process=[isolate_core_concepts(), identify_basic_relationships()]; constraints=[essential_concepts_only()]; requirements=[core_concept_identification()]; output={concepts:dict}}`",
      "step": "c",
      "title": "Concept Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"simplified_text\": \"Artificial intelligence is a powerful technology that uses computer programs to do tasks like learning, recognizing patterns, understanding language, and seeing images. It tries to act like how humans think and make decisions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"concepts\": {\n    \"Artificial Intelligence (AI)\": \"Transformative technology simulating human cognitive functions and decision-making\",\n    \"Machine Learning Algorithms\": \"Computer programs that learn from data\",\n    \"Neural Networks\": \"Systems inspired by the human brain for pattern recognition\",\n    \"Natural Language Processing (NLP)\": \"Techniques enabling computers to understand and use human language\",\n    \"Computer Vision\": \"Technology that enables computers to interpret and understand visual information\",\n    \"Simulation of Human Cognition\": \"AI aims to mimic human thinking and decision-making processes\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:\n\n`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
      "step": "c",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"concepts\": {\n    \"Artificial Intelligence (AI)\": \"Transformative technology simulating human cognitive functions and decision-making\",\n    \"Machine Learning Algorithms\": \"Computer programs that learn from data\",\n    \"Neural Networks\": \"Systems inspired by the human brain for pattern recognition\",\n    \"Natural Language Processing (NLP)\": \"Techniques enabling computers to understand and use human language\",\n    \"Computer Vision\": \"Technology that enables computers to interpret and understand visual information\",\n    \"Simulation of Human Cognition\": \"AI aims to mimic human thinking and decision-making processes\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"definition\"\n}",
        }
      }
    },
      {
      "instruction": "# Text Simplifier\n\nYour goal is not to **change** the meaning, but to **simplify** the essential message. Execute as:\n\n`{role=essential_text_simplifier; input=[text:str]; process=[extract_core_message(), use_basic_language()]; constraints=[essential_simplification_only()]; requirements=[clear_core_message()]; output={simplified_text:str}}`",
      "step": "c",
      "title": "Text Simplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"what_it_is\": \"definition\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"simplified_text\": \"Artificial intelligence is a technology that uses computer systems to do things like learn, understand language, and see images, similar to how humans think and make decisions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Concept Extractor\n\nYour goal is not to **elaborate** but to **identify** core concepts. Execute as:\n\n`{role=core_concept_extractor; input=[content:any]; process=[find_central_concepts()]; output={concepts:list}}`",
      "step": "d",
      "title": "Concept Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"simplified_text\": \"Artificial intelligence is a technology that uses computer systems to do things like learn, understand language, and see images, similar to how humans think and make decisions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"concepts\": [\n    \"artificial intelligence\",\n    \"machine learning algorithms\",\n    \"neural networks\",\n    \"natural language processing\",\n    \"computer vision\",\n    \"simulation of human cognition\",\n    \"decision-making processes\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:\n\n`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
      "step": "d",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"concepts\": [\n    \"artificial intelligence\",\n    \"machine learning algorithms\",\n    \"neural networks\",\n    \"natural language processing\",\n    \"computer vision\",\n    \"simulation of human cognition\",\n    \"decision-making processes\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"what_it_is\": \"technology\"}",
        }
      }
    },
      {
      "instruction": "# Text Simplifier\n\nYour goal is not to **expand** but to **distill** to simplest form. Execute as:\n\n`{role=minimal_simplifier; input=[text:str]; process=[find_simplest_expression()]; output={simple_text:str}}`",
      "step": "d",
      "title": "Text Simplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Artificial intelligence represents a transformative technology that encompasses machine learning algorithms, neural networks, natural language processing, and computer vision systems designed to simulate human cognitive functions and decision-making processes.\\\"```'\\n\\n\",\n  \"what_it_is\": \"technology\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"simple_text\": \"Artificial intelligence is technology that makes computers act like humans.\"\n}",
        }
      }
    }