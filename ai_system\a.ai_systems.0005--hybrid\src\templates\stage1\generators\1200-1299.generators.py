#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import os
import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import TemplateConfig

OUTPUT_DIR = Path(__file__).parent.parent / "md"

# Get stage configuration for this generator
CURRENT_STAGE = "stage1"
STAGE_CONFIG = TemplateConfig.STAGES[CURRENT_STAGE]
STAGE_RANGE = STAGE_CONFIG["range"]
AUTO_ID_ENABLED = STAGE_CONFIG["auto_id"]

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.

    "a-runway_prompt_generator1": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:",
        "transformation": "`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
    },
    "b-runway_prompt_generator1": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:",
        "transformation": "`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
    },
    "c-runway_prompt_generator1": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:",
        "transformation": "`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
    },
    "d-runway_prompt_generator1": {
        "title": "Runway Prompt Generator",
        "interpretation": "Your goal is not to **modify** but to **essence** maximum FPV impact. Execute as:",
        "transformation": "`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
    },

    "a-runway_prompt_generator2": {
        "title": "Visual Scene Architect",
        "interpretation": "Your goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:",
        "transformation": "`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`",
    },
    "b-runway_prompt_generator2": {
        "title": "Motion & Animation Designer",
        "interpretation": "Your goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:",
        "transformation": "`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`",
    },
    "c-runway_prompt_generator2": {
        "title": "Cinematography Director",
        "interpretation": "Your goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:",
        "transformation": "`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`",
    },
    "d-runway_prompt_generator2": {
        "title": "Runway Optimization Specialist",
        "interpretation": "Your goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:",
        "transformation": "`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`",
    },

}

def check_sequence_exists(auto_templates):
    """Check if the exact same sequence already exists."""
    import json

    catalog_path = OUTPUT_DIR.parent.parent / "lvl1.md.templates.json"
    if not catalog_path.exists():
        return False, None

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
            templates = catalog.get('templates', {})

        # Group existing templates by sequence ID
        existing_sequences = {}
        for template_id, template_data in templates.items():
            if '-' in template_id:
                seq_id = template_id.split('-')[0]
                step = template_id.split('-')[1]
                if seq_id not in existing_sequences:
                    existing_sequences[seq_id] = {}
                existing_sequences[seq_id][step] = template_data

        # Check if any existing sequence matches our auto_templates
        for seq_id, existing_steps in existing_sequences.items():
            if len(existing_steps) == len(auto_templates):
                match = True
                for auto_key, auto_template in auto_templates.items():
                    step = auto_key.split('-')[0]  # Get 'a', 'b', 'c', 'd'
                    if step in existing_steps:
                        existing_template = existing_steps[step]
                        # Compare title and transformation (core content)
                        if (existing_template.get('title') != auto_template['title'] or
                            existing_template.get('transformation') != auto_template['transformation']):
                            match = False
                            break
                    else:
                        match = False
                        break

                if match:
                    return True, seq_id

    except Exception:
        pass

    return False, None

def get_next_available_id(used_ids_cache=None):
    """Get the next available ID in the current stage range."""
    import json

    # Check if auto-ID is enabled for this stage
    if not AUTO_ID_ENABLED:
        print(f"ERROR: Auto-ID is disabled for {CURRENT_STAGE}")
        return None

    # Use cached used_ids if provided, otherwise load from catalog
    if used_ids_cache is not None:
        used_ids = used_ids_cache
    else:
        # Try to load existing catalog to find used IDs
        catalog_path = OUTPUT_DIR.parent.parent / "lvl1.md.templates.json"
        used_ids = set()

        if catalog_path.exists():
            try:
                with open(catalog_path, 'r', encoding='utf-8') as f:
                    catalog = json.load(f)
                    templates = catalog.get('templates', {})
                    for template_id in templates.keys():
                        if template_id.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):  # Stage IDs
                            id_num = int(template_id.split('-')[0])
                            if STAGE_RANGE[0] <= id_num <= STAGE_RANGE[1]:  # Current stage range
                                used_ids.add(id_num)
            except Exception:
                pass

    # Find next available ID in current stage range
    for id_num in range(STAGE_RANGE[0], STAGE_RANGE[1] + 1):
        if id_num not in used_ids:
            used_ids.add(id_num)  # Mark as used for subsequent calls
            return id_num

    return None  # No available IDs

def create_template_files():
    """Generate markdown template files with unified auto-ID detection."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []

    # Separate templates by ID type and group auto-ID templates by sequence
    auto_id_sequences = {}
    manual_id_templates = {}

    for template_key, template in TEMPLATES.items():
        if template_key.startswith(('a-', 'b-', 'c-', 'd-', 'e-', 'f-', 'g-', 'h-')):
            # Auto-ID template (starts with letter-dash)
            # Extract sequence name: "a-concept_extractor" -> "concept_extractor"
            sequence_name = template_key.split('-', 1)[1]
            if sequence_name not in auto_id_sequences:
                auto_id_sequences[sequence_name] = {}
            auto_id_sequences[sequence_name][template_key] = template
        else:
            # Manual ID template (already has numeric ID)
            manual_id_templates[template_key] = template

    # Initialize used IDs cache for multiple sequence processing
    used_ids_cache = set()

    # Process each auto-ID sequence separately
    for sequence_name, sequence_templates in auto_id_sequences.items():
        # Check if this exact sequence already exists
        exists, existing_id = check_sequence_exists(sequence_templates)

        if exists:
            print(f"SKIP: {sequence_name} sequence already exists as {existing_id}")
            # Still add to created_files list for consistency
            for template_key in sequence_templates.keys():
                filename = f"{existing_id}-{template_key}"
                created_files.append(f"{filename}.md")
        else:
            # Generate new sequence with auto-ID
            next_id = get_next_available_id(used_ids_cache)
            if next_id is None:
                print(f"ERROR: No available IDs in {CURRENT_STAGE} range ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
                return created_files

            print(f"AUTO-ID: Using {next_id} for {sequence_name} sequence")

            for template_key, template in sequence_templates.items():
                # Convert "a-concept_extractor" to "1004-a-concept_extractor"
                filename = f"{next_id}-{template_key}"
                filepath = OUTPUT_DIR / f"{filename}.md"
                content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(content)

                created_files.append(f"{filename}.md")

    # Process manual ID templates
    for filename, template in manual_id_templates.items():
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files

def main():
    """Main execution function."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass

    # Display stage configuration
    print(f"STAGE: {CURRENT_STAGE.upper()} ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
    print(f"AUTO-ID: {'Enabled' if AUTO_ID_ENABLED else 'Disabled'}")
    print(f"DESCRIPTION: {STAGE_CONFIG['description']}")
    print()

    created_files = create_template_files()

    print(f"SUCCESS: Created {CURRENT_STAGE} identifier templates:")
    for file in created_files:
        print(f"   - {file}")

    print(f"\nLOCATION: Templates generated in: {OUTPUT_DIR}")
    print(f"\nSTAGE RANGE: {STAGE_RANGE[0]}-{STAGE_RANGE[1]} ({STAGE_CONFIG['description']})")
    print("SEQUENCE PATTERN: Progressive compression (a->b->c->d)")
    print("   Step a: Comprehensive analysis")
    print("   Step b: Focused distillation")
    print("   Step c: Essential compression")
    print("   Step d: Absolute essence")

if __name__ == "__main__":
    main()
