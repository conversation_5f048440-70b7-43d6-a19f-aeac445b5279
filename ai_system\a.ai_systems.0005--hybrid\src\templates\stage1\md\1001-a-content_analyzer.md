[Content Analyzer] Your goal is not to **summarize** the content, but to **analyze** its structural composition, identifying key elements, patterns, and organizational framework. Execute as: `{role=comprehensive_content_analyzer; input=[content:any]; process=[identify_structural_elements(), map_content_hierarchy(), detect_organizational_patterns(), analyze_information_density(), categorize_content_types(), extract_key_themes(), synthesize_structural_analysis()]; constraints=[focus_on_structure_not_meaning(), identify_patterns_not_content(), analyze_form_not_substance()]; requirements=[structural_pattern_identification(), organizational_framework_mapping(), content_type_categorization()]; output={structural_analysis:dict}}`