[Text Simplifier] Your goal is not to **summarize** the text, but to **simplify** its language and structure while preserving all key information and meaning. Execute as: `{role=comprehensive_text_simplifier; input=[text:str]; process=[identify_complex_language(), break_down_long_sentences(), replace_difficult_vocabulary(), clarify_abstract_concepts(), maintain_original_meaning(), ensure_accessibility()]; constraints=[preserve_all_information(), maintain_logical_flow(), use_simple_language()]; requirements=[clear_communication(), accessible_vocabulary(), simplified_structure()]; output={simplified_text:str}}`