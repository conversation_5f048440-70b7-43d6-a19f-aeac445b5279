{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.06.12-kl.23.53", "source_directory": "stage1/md", "total_templates": 8, "total_sequences": 2, "stage_distribution": {"stage1": {"count": 8, "description": "Prototyping/Testing", "range": [1000, 1999], "auto_id": true, "templates": ["1004-a-text_simplifier", "1004-b-text_simplifier", "1004-c-text_simplifier", "1004-d-text_simplifier", "1031-a-form_classifier", "1031-b-form_classifier", "1031-c-form_classifier", "1031-d-form_classifier"]}}}, "templates": {"1004-a-text_simplifier": {"raw": "[Text Simplifier] Your goal is not to **summarize** the text, but to **simplify** its language and structure while preserving all key information and meaning. Execute as: `{role=comprehensive_text_simplifier; input=[text:str]; process=[identify_complex_language(), break_down_long_sentences(), replace_difficult_vocabulary(), clarify_abstract_concepts(), maintain_original_meaning(), ensure_accessibility()]; constraints=[preserve_all_information(), maintain_logical_flow(), use_simple_language()]; requirements=[clear_communication(), accessible_vocabulary(), simplified_structure()]; output={simplified_text:str}}`", "parts": {"title": "Text Simplifier", "interpretation": "Your goal is not to **summarize** the text, but to **simplify** its language and structure while preserving all key information and meaning. Execute as:", "transformation": "`{role=comprehensive_text_simplifier; input=[text:str]; process=[identify_complex_language(), break_down_long_sentences(), replace_difficult_vocabulary(), clarify_abstract_concepts(), maintain_original_meaning(), ensure_accessibility()]; constraints=[preserve_all_information(), maintain_logical_flow(), use_simple_language()]; requirements=[clear_communication(), accessible_vocabulary(), simplified_structure()]; output={simplified_text:str}}`", "keywords": "structure"}}, "1004-b-text_simplifier": {"raw": "[Text Simplifier] Your goal is not to **rewrite** the text, but to **simplify** its most complex elements while keeping the core message clear. Execute as: `{role=focused_text_simplifier; input=[text:str]; process=[identify_main_complexity_barriers(), simplify_key_concepts(), clarify_essential_points()]; constraints=[focus_on_major_simplifications(), preserve_core_meaning()]; requirements=[improved_clarity(), accessible_language()]; output={simplified_text:str}}`", "parts": {"title": "Text Simplifier", "interpretation": "Your goal is not to **rewrite** the text, but to **simplify** its most complex elements while keeping the core message clear. Execute as:", "transformation": "`{role=focused_text_simplifier; input=[text:str]; process=[identify_main_complexity_barriers(), simplify_key_concepts(), clarify_essential_points()]; constraints=[focus_on_major_simplifications(), preserve_core_meaning()]; requirements=[improved_clarity(), accessible_language()]; output={simplified_text:str}}`", "keywords": ""}}, "1004-c-text_simplifier": {"raw": "[Text Simplifier] Your goal is not to **change** the meaning, but to **simplify** the essential message. Execute as: `{role=essential_text_simplifier; input=[text:str]; process=[extract_core_message(), use_basic_language()]; constraints=[essential_simplification_only()]; requirements=[clear_core_message()]; output={simplified_text:str}}`", "parts": {"title": "Text Simplifier", "interpretation": "Your goal is not to **change** the meaning, but to **simplify** the essential message. Execute as:", "transformation": "`{role=essential_text_simplifier; input=[text:str]; process=[extract_core_message(), use_basic_language()]; constraints=[essential_simplification_only()]; requirements=[clear_core_message()]; output={simplified_text:str}}`", "keywords": ""}}, "1004-d-text_simplifier": {"raw": "[Text Simplifier] Your goal is not to **expand** but to **distill** to simplest form. Execute as: `{role=minimal_simplifier; input=[text:str]; process=[find_simplest_expression()]; output={simple_text:str}}`", "parts": {"title": "Text Simplifier", "interpretation": "Your goal is not to **expand** but to **distill** to simplest form. Execute as:", "transformation": "`{role=minimal_simplifier; input=[text:str]; process=[find_simplest_expression()]; output={simple_text:str}}`", "keywords": "distill"}}, "1031-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-b-form_classifier": {"raw": "[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:", "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "keywords": "distill"}}, "1031-c-form_classifier": {"raw": "[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:", "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-d-form_classifier": {"raw": "[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:", "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "keywords": "essence"}}}, "sequences": {"1004": [{"template_id": "1004-a-text_simplifier", "step": "a", "order": 0}, {"template_id": "1004-b-text_simplifier", "step": "b", "order": 1}, {"template_id": "1004-c-text_simplifier", "step": "c", "order": 2}, {"template_id": "1004-d-text_simplifier", "step": "d", "order": 3}], "1031": [{"template_id": "1031-a-form_classifier", "step": "a", "order": 0}, {"template_id": "1031-b-form_classifier", "step": "b", "order": 1}, {"template_id": "1031-c-form_classifier", "step": "c", "order": 2}, {"template_id": "1031-d-form_classifier", "step": "d", "order": 3}]}}