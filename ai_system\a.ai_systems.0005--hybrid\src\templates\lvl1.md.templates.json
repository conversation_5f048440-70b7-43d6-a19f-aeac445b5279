{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.06.13-kl.00.05", "source_directory": "stage1/md", "total_templates": 12, "total_sequences": 3, "stage_distribution": {"stage1": {"count": 12, "description": "Prototyping/Testing", "range": [1000, 1999], "auto_id": true, "templates": ["1000-a-concept_extractor", "1000-b-concept_extractor", "1000-c-concept_extractor", "1000-d-concept_extractor", "1001-a-text_enhancer", "1001-b-text_enhancer", "1001-c-text_enhancer", "1001-d-text_enhancer", "1002-a-form_classifier", "1002-b-form_classifier", "1002-c-form_classifier", "1002-d-form_classifier"]}}}, "templates": {"1000-a-concept_extractor": {"raw": "[Concept Extractor] Your goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as: `{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`", "parts": {"title": "Concept Extractor", "interpretation": "Your goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:", "transformation": "`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`", "keywords": ""}}, "1000-b-concept_extractor": {"raw": "[Concept Extractor] Your goal is not to **describe** the content, but to **extract** the primary concepts and their main relationships. Execute as: `{role=focused_concept_extractor; input=[content:any]; process=[identify_primary_concepts(), extract_main_relationships(), categorize_concept_types()]; constraints=[focus_on_primary_concepts(), ignore_minor_details()]; requirements=[clear_concept_identification(), relationship_clarity()]; output={concepts:dict}}`", "parts": {"title": "Concept Extractor", "interpretation": "Your goal is not to **describe** the content, but to **extract** the primary concepts and their main relationships. Execute as:", "transformation": "`{role=focused_concept_extractor; input=[content:any]; process=[identify_primary_concepts(), extract_main_relationships(), categorize_concept_types()]; constraints=[focus_on_primary_concepts(), ignore_minor_details()]; requirements=[clear_concept_identification(), relationship_clarity()]; output={concepts:dict}}`", "keywords": ""}}, "1000-c-concept_extractor": {"raw": "[Concept Extractor] Your goal is not to **explain** the content, but to **extract** the essential concepts. Execute as: `{role=essential_concept_extractor; input=[content:any]; process=[isolate_core_concepts(), identify_basic_relationships()]; constraints=[essential_concepts_only()]; requirements=[core_concept_identification()]; output={concepts:dict}}`", "parts": {"title": "Concept Extractor", "interpretation": "Your goal is not to **explain** the content, but to **extract** the essential concepts. Execute as:", "transformation": "`{role=essential_concept_extractor; input=[content:any]; process=[isolate_core_concepts(), identify_basic_relationships()]; constraints=[essential_concepts_only()]; requirements=[core_concept_identification()]; output={concepts:dict}}`", "keywords": ""}}, "1000-d-concept_extractor": {"raw": "[Concept Extractor] Your goal is not to **elaborate** but to **identify** core concepts. Execute as: `{role=core_concept_extractor; input=[content:any]; process=[find_central_concepts()]; output={concepts:list}}`", "parts": {"title": "Concept Extractor", "interpretation": "Your goal is not to **elaborate** but to **identify** core concepts. Execute as:", "transformation": "`{role=core_concept_extractor; input=[content:any]; process=[find_central_concepts()]; output={concepts:list}}`", "keywords": ""}}, "1001-a-text_enhancer": {"raw": "[Text Enhancer] Your goal is not to **change** the meaning, but to **enhance** the text by improving clarity, flow, and impact while preserving the original message. Execute as: `{role=comprehensive_text_enhancer; input=[text:str]; process=[improve_clarity(), enhance_flow(), strengthen_impact(), refine_word_choice(), optimize_sentence_structure(), maintain_original_meaning()]; constraints=[preserve_core_message(), maintain_author_voice(), improve_readability()]; requirements=[enhanced_clarity(), improved_flow(), stronger_impact()]; output={enhanced_text:str}}`", "parts": {"title": "Text Enhancer", "interpretation": "Your goal is not to **change** the meaning, but to **enhance** the text by improving clarity, flow, and impact while preserving the original message. Execute as:", "transformation": "`{role=comprehensive_text_enhancer; input=[text:str]; process=[improve_clarity(), enhance_flow(), strengthen_impact(), refine_word_choice(), optimize_sentence_structure(), maintain_original_meaning()]; constraints=[preserve_core_message(), maintain_author_voice(), improve_readability()]; requirements=[enhanced_clarity(), improved_flow(), stronger_impact()]; output={enhanced_text:str}}`", "keywords": "clarity"}}, "1001-b-text_enhancer": {"raw": "[Text Enhancer] Your goal is not to **rewrite** the text, but to **enhance** its key elements for better clarity and impact. Execute as: `{role=focused_text_enhancer; input=[text:str]; process=[improve_key_clarity_points(), enhance_main_flow(), strengthen_core_impact()]; constraints=[focus_on_major_improvements(), preserve_original_meaning()]; requirements=[clearer_communication(), better_flow()]; output={enhanced_text:str}}`", "parts": {"title": "Text Enhancer", "interpretation": "Your goal is not to **rewrite** the text, but to **enhance** its key elements for better clarity and impact. Execute as:", "transformation": "`{role=focused_text_enhancer; input=[text:str]; process=[improve_key_clarity_points(), enhance_main_flow(), strengthen_core_impact()]; constraints=[focus_on_major_improvements(), preserve_original_meaning()]; requirements=[clearer_communication(), better_flow()]; output={enhanced_text:str}}`", "keywords": "clarity"}}, "1001-c-text_enhancer": {"raw": "[Text Enhancer] Your goal is not to **alter** the meaning, but to **enhance** essential clarity. Execute as: `{role=essential_text_enhancer; input=[text:str]; process=[improve_core_clarity(), enhance_basic_flow()]; constraints=[essential_improvements_only()]; requirements=[clearer_core_message()]; output={enhanced_text:str}}`", "parts": {"title": "Text Enhancer", "interpretation": "Your goal is not to **alter** the meaning, but to **enhance** essential clarity. Execute as:", "transformation": "`{role=essential_text_enhancer; input=[text:str]; process=[improve_core_clarity(), enhance_basic_flow()]; constraints=[essential_improvements_only()]; requirements=[clearer_core_message()]; output={enhanced_text:str}}`", "keywords": "clarity"}}, "1001-d-text_enhancer": {"raw": "[Text Enhancer] Your goal is not to **complicate** but to **clarify** essence. Execute as: `{role=clarity_enhancer; input=[text:str]; process=[maximize_clarity()]; output={clear_text:str}}`", "parts": {"title": "Text Enhancer", "interpretation": "Your goal is not to **complicate** but to **clarify** essence. Execute as:", "transformation": "`{role=clarity_enhancer; input=[text:str]; process=[maximize_clarity()]; output={clear_text:str}}`", "keywords": "essence"}}, "1002-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "keywords": ""}}, "1002-b-form_classifier": {"raw": "[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:", "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "keywords": "distill"}}, "1002-c-form_classifier": {"raw": "[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:", "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "keywords": ""}}, "1002-d-form_classifier": {"raw": "[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:", "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "keywords": "essence"}}}, "sequences": {"1000": [{"template_id": "1000-a-concept_extractor", "step": "a", "order": 0}, {"template_id": "1000-b-concept_extractor", "step": "b", "order": 1}, {"template_id": "1000-c-concept_extractor", "step": "c", "order": 2}, {"template_id": "1000-d-concept_extractor", "step": "d", "order": 3}], "1001": [{"template_id": "1001-a-text_enhancer", "step": "a", "order": 0}, {"template_id": "1001-b-text_enhancer", "step": "b", "order": 1}, {"template_id": "1001-c-text_enhancer", "step": "c", "order": 2}, {"template_id": "1001-d-text_enhancer", "step": "d", "order": 3}], "1002": [{"template_id": "1002-a-form_classifier", "step": "a", "order": 0}, {"template_id": "1002-b-form_classifier", "step": "b", "order": 1}, {"template_id": "1002-c-form_classifier", "step": "c", "order": 2}, {"template_id": "1002-d-form_classifier", "step": "d", "order": 3}]}}