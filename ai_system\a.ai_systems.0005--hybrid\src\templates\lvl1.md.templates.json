{"catalog_meta": {"level": "lvl1", "format": "md", "generated_at": "2025.06.12-kl.23.42", "source_directory": "stage1/md", "total_templates": 8, "total_sequences": 2, "stage_distribution": {"stage1": {"count": 8, "description": "Prototyping/Testing", "range": [1000, 1999], "auto_id": true, "templates": ["1000-a-content_analyzer", "1000-b-content_analyzer", "1000-c-content_analyzer", "1000-d-content_analyzer", "1031-a-form_classifier", "1031-b-form_classifier", "1031-c-form_classifier", "1031-d-form_classifier"]}}}, "templates": {"1000-a-content_analyzer": {"raw": "[Content Analyzer] Your goal is not to **summarize** the content, but to **analyze** its structural composition, identifying key elements, patterns, and organizational framework. Execute as: `{role=comprehensive_content_analyzer; input=[content:any]; process=[identify_structural_elements(), map_content_hierarchy(), detect_organizational_patterns(), analyze_information_density(), categorize_content_types(), extract_key_themes(), synthesize_structural_analysis()]; constraints=[focus_on_structure_not_meaning(), identify_patterns_not_content(), analyze_form_not_substance()]; requirements=[structural_pattern_identification(), organizational_framework_mapping(), content_type_categorization()]; output={structural_analysis:dict}}`", "parts": {"title": "Content Analyzer", "interpretation": "Your goal is not to **summarize** the content, but to **analyze** its structural composition, identifying key elements, patterns, and organizational framework. Execute as:", "transformation": "`{role=comprehensive_content_analyzer; input=[content:any]; process=[identify_structural_elements(), map_content_hierarchy(), detect_organizational_patterns(), analyze_information_density(), categorize_content_types(), extract_key_themes(), synthesize_structural_analysis()]; constraints=[focus_on_structure_not_meaning(), identify_patterns_not_content(), analyze_form_not_substance()]; requirements=[structural_pattern_identification(), organizational_framework_mapping(), content_type_categorization()]; output={structural_analysis:dict}}`", "keywords": ""}}, "1000-b-content_analyzer": {"raw": "[Content Analyzer] Your goal is not to **describe** the content, but to **analyze** its primary structural patterns and organizational elements. Execute as: `{role=focused_content_analyzer; input=[content:any]; process=[identify_primary_structure(), extract_main_patterns(), categorize_organization_type()]; constraints=[focus_on_primary_elements(), ignore_secondary_details()]; requirements=[clear_structural_identification(), pattern_recognition()]; output={structural_analysis:dict}}`", "parts": {"title": "Content Analyzer", "interpretation": "Your goal is not to **describe** the content, but to **analyze** its primary structural patterns and organizational elements. Execute as:", "transformation": "`{role=focused_content_analyzer; input=[content:any]; process=[identify_primary_structure(), extract_main_patterns(), categorize_organization_type()]; constraints=[focus_on_primary_elements(), ignore_secondary_details()]; requirements=[clear_structural_identification(), pattern_recognition()]; output={structural_analysis:dict}}`", "keywords": ""}}, "1000-c-content_analyzer": {"raw": "[Content Analyzer] Your goal is not to **explain** the content, but to **analyze** its essential structural elements. Execute as: `{role=essential_content_analyzer; input=[content:any]; process=[isolate_core_structure(), identify_basic_patterns()]; constraints=[essential_elements_only()]; requirements=[basic_structural_identification()]; output={structural_analysis:dict}}`", "parts": {"title": "Content Analyzer", "interpretation": "Your goal is not to **explain** the content, but to **analyze** its essential structural elements. Execute as:", "transformation": "`{role=essential_content_analyzer; input=[content:any]; process=[isolate_core_structure(), identify_basic_patterns()]; constraints=[essential_elements_only()]; requirements=[basic_structural_identification()]; output={structural_analysis:dict}}`", "keywords": ""}}, "1000-d-content_analyzer": {"raw": "[Content Analyzer] Your goal is not to **elaborate** but to **identify** core structure. Execute as: `{role=core_analyzer; input=[content:any]; process=[find_structural_essence()]; output={structure:str}}`", "parts": {"title": "Content Analyzer", "interpretation": "Your goal is not to **elaborate** but to **identify** core structure. Execute as:", "transformation": "`{role=core_analyzer; input=[content:any]; process=[find_structural_essence()]; output={structure:str}}`", "keywords": "structure"}}, "1031-a-form_classifier": {"raw": "[Form Classifier] Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as: `{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:", "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-b-form_classifier": {"raw": "[Form Classifier] Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as: `{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:", "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`", "keywords": "distill"}}, "1031-c-form_classifier": {"raw": "[Form Classifier] Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as: `{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:", "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`", "keywords": ""}}, "1031-d-form_classifier": {"raw": "[Form Classifier] Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as: `{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "parts": {"title": "Form Classifier", "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:", "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`", "keywords": "essence"}}}, "sequences": {"1000": [{"template_id": "1000-a-content_analyzer", "step": "a", "order": 0}, {"template_id": "1000-b-content_analyzer", "step": "b", "order": 1}, {"template_id": "1000-c-content_analyzer", "step": "c", "order": 2}, {"template_id": "1000-d-content_analyzer", "step": "d", "order": 3}], "1031": [{"template_id": "1031-a-form_classifier", "step": "a", "order": 0}, {"template_id": "1031-b-form_classifier", "step": "b", "order": 1}, {"template_id": "1031-c-form_classifier", "step": "c", "order": 2}, {"template_id": "1031-d-form_classifier", "step": "d", "order": 3}]}}