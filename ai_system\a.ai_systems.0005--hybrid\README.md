
# AI Systems 0005 - Hybrid Template System

## 🎯 **Universal Template-Based Instruction Processing**

A revolutionary system that transforms any instruction or prompt into a standardized, machine-parsable, three-part canonical structure that can be executed in sequences and chained together.

### **Core Innovation**
- **Universal directive system** with syntactic transformation focus
- **Stage-based template organization** with automatic ID generation
- **Sequence execution engine** with type-safe chaining
- **Platform-agnostic execution** across any AI model

## 📊 **Stage-Based Organization**

### **Stage Ranges & Purposes**
```
Stage 1: 1000-1999 | Prototyping/Testing    | Auto-ID: ✓
Stage 2: 2000-2999 | Validated/Unplaced     | Auto-ID: ✗
Stage 3: 3000-3999 | Finalized/Production   | Auto-ID: ✗
Stage 4: 4000-4999 | Reserved               | Auto-ID: ✗
Stage 5: 5000-5999 | Reserved               | Auto-ID: ✗
Stage 6: 6000-6999 | Reserved               | Auto-ID: ✗
Stage 7: 7000-7999 | Reserved               | Auto-ID: ✗
Stage 8: 8000-8999 | Reserved               | Auto-ID: ✗
Stage 9: 9000-9999 | Reserved               | Auto-ID: ✗
```

### **Template Organization**
```
# FILE ORGANIZATION
templates/lvl1/md/
├── {stage_id}-{step}-{template_name}.md
├── {stage_id}-{step}-{template_name}.md
└── {stage_id}-{step}-{template_name}.md

# EXAMPLES
templates/lvl1/md/
├── 1010-a-title_extractor.md
├── 1010-b-title_extractor.md
├── 3001-a-bracketed_keyword_infuser.md
└── 3005-a-syntactic_pattern_analyzer.md
```

## 🔧 **Three-Part Canonical Structure**

Every template follows this invariant pattern:
```
[Title] Interpretation Execute as: `{Transformation}`
  │      │              │         └─ Machine-parsable parameters
  │      │              └─ Standard connector phrase
  │      └─ Goal negation + transformation declaration
  └─ Brief identifier in brackets
```

### **Goal Negation Pattern**
The revolutionary approach of explicitly stating what NOT to do first:
```
"Your goal is not to **[action]** the input, but to **[transformation_action]** it..."
```

### **Syntactic Structure Focus**
Rather than domain-specific knowledge, the system focuses on **syntactic patterns**:
- `[keyword]` - Bracketed directives
- `[keyword:value]` - Parameterized directives
- `**text**` - Emphasis markers
- Structured transformation rules

## 🚀 **Quick Start**

### **Stage Management**
```bash
# Show complete stage overview
python src/templates/lvl1_md_to_json.py --stage-overview

# Get next available prototype ID
python src/templates/lvl1_md_to_json.py --next-stage1-id

# Validate stage compliance
python src/templates/lvl1_md_to_json.py --validate-stages

# Show migration suggestions
python src/templates/lvl1_md_to_json.py --migration-suggestions
```

### **Template Generation**
```bash
# Generate templates by category
python src/templates/lvl1.md.generate.transformers.py
python src/templates/lvl1.md.generate.extractors.py
python src/templates/lvl1.md.generate.generators.py

# Regenerate catalog
python src/templates/lvl1_md_to_json.py --force
```

### **Template Execution**
```bash
# Execute single template
python src/lvl1_sequence_executor.py --sequence 3001 --prompt "Your input text"

# Execute template sequence
python src/lvl1_sequence_executor.py --sequence 1010|3001|3005 --prompt "Your input text"
```

## 📋 **Template Examples**

### **Bracketed Keyword Infuser (3001)**
```
[Bracketed Keyword Infuser] Your goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as: `{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`
```

### **Syntactic Pattern Analyzer (3005)**
```
[Syntactic Pattern Analyzer] Your goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as: `{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`
```

## 🔄 **Workflow**

### **1. Prototyping (Stage 1)**
- Get next auto-ID for new template
- Create template with auto-generated ID
- Test and iterate in Stage 1 range
- No manual ID management required

### **2. Validation (Stage 2)**
- Move proven templates from Stage 1
- Templates are validated but not yet categorized
- Manual ID assignment for organization
- Prepare for final production placement

### **3. Production (Stage 3)**
- Finalized, production-ready templates
- Stable IDs that won't change
- Full compliance with RulesForAI.md
- Ready for system integration

## 🏗️ **System Architecture**

### **Template Categories**
- **Extractors (1000-1100)**: Extract specific elements from input
- **Generators (2000-2100)**: Generate content from scratch
- **Transformers (3000-3100)**: Transform input content using patterns
- **Classifiers (0500-0600)**: Categorize and classify content
- **Runway Templates (8000-8100)**: Specialized visual generation

### **Key Features**
- ✅ **Universal Compatibility**: Works across any AI model provider
- ✅ **Composability**: Templates can be chained together in sequences
- ✅ **Type Safety**: Built-in type checking for inputs/outputs
- ✅ **Systematic Validation**: Automatic compliance checking
- ✅ **Scalable Architecture**: Stage-based organization with expansion capacity

---

**Ready for systematic template management and AI instruction processing at scale.**
