#!/usr/bin/env python3

"""
Stage 3 Finalizers Generator
3000-3199: Finalized Production Templates

Stage 3 templates are production-ready with stable IDs and full compliance.
Auto-ID is disabled - all templates require deliberate manual ID assignment.
"""

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import TemplateConfig

OUTPUT_DIR = Path(__file__).parent.parent / "md"

# Get stage configuration for this generator
CURRENT_STAGE = "stage3"
STAGE_CONFIG = TemplateConfig.STAGES[CURRENT_STAGE]
STAGE_RANGE = STAGE_CONFIG["range"]
AUTO_ID_ENABLED = STAGE_CONFIG["auto_id"]

TEMPLATES = {
    # Production Templates (Stage 3): Finalized and stable
    # Must specify exact ID in format: "3001-a-template_name"
    # These represent the crystallized, production-ready form classifier
    
    "3001-a-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **interpret** content meaning, but to **identify** the fundamental structural form with comprehensive analysis and complete accuracy. Execute as:",
        "transformation": "`{role=production_form_identifier; input=[content:any]; process=[analyze_structural_elements(), identify_communication_patterns(), determine_document_type(), classify_format_structure(), validate_form_category(), ensure_classification_accuracy(), provide_comprehensive_identification()]; constraints=[focus_on_structural_form(), ignore_semantic_content(), maintain_classification_precision(), ensure_production_quality()]; requirements=[accurate_form_identification(), comprehensive_structural_analysis(), validated_classification_output(), production_grade_reliability()]; output={form_classification:str, confidence_level:float, structural_elements:list}}`"
    },

    "3001-b-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **describe** content details, but to **identify** the primary structural form with focused precision and reliability. Execute as:",
        "transformation": "`{role=focused_production_identifier; input=[content:any]; process=[identify_primary_structure(), classify_main_form_type(), validate_classification(), ensure_accuracy()]; constraints=[focus_on_primary_form_elements(), maintain_production_standards(), ensure_reliability()]; requirements=[precise_form_identification(), validated_classification(), production_quality_output()]; output={form_classification:str, confidence_level:float}}`"
    },

    "3001-c-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **elaborate** on content, but to **identify** the essential structural form with production-grade precision. Execute as:",
        "transformation": "`{role=essential_production_identifier; input=[content:any]; process=[isolate_core_structural_form(), validate_essential_classification(), ensure_production_accuracy()]; constraints=[essential_form_elements_only(), maintain_production_standards()]; requirements=[accurate_core_identification(), production_grade_output()]; output={form_classification:str, confidence_level:float}}`"
    },

    "3001-d-production_form_classifier": {
        "title": "Production Form Classifier",
        "interpretation": "Your goal is not to **expand** analysis, but to **identify** the absolute structural essence with maximum precision. Execute as:",
        "transformation": "`{role=absolute_production_identifier; input=[content:any]; process=[determine_absolute_form_essence(), validate_final_classification()]; constraints=[absolute_precision_required(), production_grade_standards()]; requirements=[maximum_accuracy(), final_form_identification()]; output={form_classification:str, confidence_level:float}}`"
    },


}

def validate_production_template(template_key, template):
    """Validate that template meets production standards."""
    issues = []
    
    # Check for required fields
    required_fields = ['title', 'interpretation', 'transformation']
    for field in required_fields:
        if field not in template:
            issues.append(f"Missing required field: {field}")
    
    # Check interpretation format (goal negation pattern)
    if 'interpretation' in template:
        interp = template['interpretation']
        if 'Your goal is not to **' not in interp or 'but to **' not in interp:
            issues.append("Interpretation missing goal negation pattern")
    
    # Check transformation format (structured block)
    if 'transformation' in template:
        trans = template['transformation']
        if not (trans.startswith('`{') and trans.endswith('}`')):
            issues.append("Transformation not in structured block format")
        if 'role=' not in trans or 'output=' not in trans:
            issues.append("Transformation missing required role/output parameters")
    
    return issues

def create_template_files():
    """Generate markdown template files with stage-aware processing and validation."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []
    validation_errors = []
    
    # Check if auto-ID is enabled for this stage
    if not AUTO_ID_ENABLED:
        print(f"INFO: Auto-ID is disabled for {CURRENT_STAGE} - using manual IDs only")
        print(f"INFO: Production-grade validation enabled for {CURRENT_STAGE}")
    
    # Separate templates by ID type
    auto_id_templates = {}
    manual_id_templates = {}
    
    for template_key, template in TEMPLATES.items():
        if template_key.startswith(('a-', 'b-', 'c-', 'd-', 'e-', 'f-', 'g-', 'h-')):
            # Auto-ID template (starts with letter-dash)
            if AUTO_ID_ENABLED:
                sequence_name = template_key.split('-', 1)[1]
                if sequence_name not in auto_id_templates:
                    auto_id_templates[sequence_name] = {}
                auto_id_templates[sequence_name][template_key] = template
            else:
                print(f"ERROR: Auto-ID template '{template_key}' found but auto-ID is disabled for {CURRENT_STAGE}")
                print(f"       Production stage requires manual ID format: 'XXXX-{template_key}' where XXXX is in range {STAGE_RANGE[0]}-{STAGE_RANGE[1]}")
                validation_errors.append(f"Auto-ID template in production stage: {template_key}")
                continue
        else:
            # Manual ID template (already has numeric ID)
            # Validate ID is in correct stage range
            try:
                id_num = int(template_key.split('-')[0])
                if not (STAGE_RANGE[0] <= id_num <= STAGE_RANGE[1]):
                    print(f"ERROR: Template '{template_key}' ID {id_num} is outside {CURRENT_STAGE} range ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
                    print(f"       Production stage requires IDs within designated range")
                    validation_errors.append(f"Out-of-range ID: {template_key}")
                    continue
            except (ValueError, IndexError):
                print(f"ERROR: Invalid template key format: '{template_key}'")
                validation_errors.append(f"Invalid key format: {template_key}")
                continue
                
            # Validate production standards
            issues = validate_production_template(template_key, template)
            if issues:
                print(f"VALIDATION ERRORS for '{template_key}':")
                for issue in issues:
                    print(f"   - {issue}")
                validation_errors.extend([f"{template_key}: {issue}" for issue in issues])
                continue
                
            manual_id_templates[template_key] = template
    
    # Process manual ID templates
    for filename, template in manual_id_templates.items():
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
            
        created_files.append(f"{filename}.md")
    
    # Report validation summary
    if validation_errors:
        print(f"\nVALIDATION SUMMARY: {len(validation_errors)} errors found")
        print("Production stage requires all templates to meet quality standards")
    else:
        print(f"\nVALIDATION SUMMARY: All templates passed production standards")
    
    return created_files

def main():
    """Main execution function."""
    import sys
    
    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except Exception:
            pass
    
    # Display stage configuration
    print(f"STAGE: {CURRENT_STAGE.upper()} ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
    print(f"AUTO-ID: {'Enabled' if AUTO_ID_ENABLED else 'Disabled'}")
    print(f"DESCRIPTION: {STAGE_CONFIG['description']}")
    print("VALIDATION: Production-grade quality standards enforced")
    print()
    
    created_files = create_template_files()
    
    print(f"\nSUCCESS: Created {CURRENT_STAGE} production templates:")
    for file in created_files:
        print(f"   - {file}")
    
    print(f"\nLOCATION: Templates generated in: {OUTPUT_DIR}")
    print(f"\nSTAGE RANGE: {STAGE_RANGE[0]}-{STAGE_RANGE[1]} ({STAGE_CONFIG['description']})")
    print("SEQUENCE PATTERN: Production-grade progressive compression")
    print("   Step a: Comprehensive production analysis")
    print("   Step b: Focused production classification")  
    print("   Step c: Essential production identification")
    print("   Step d: Absolute production precision")
    print("\nPRODUCTION FEATURES:")
    print("   - Confidence levels included in output")
    print("   - Comprehensive validation and error checking")
    print("   - Structured output with reliability metrics")
    print("   - Full RulesForAI.md compliance")


if __name__ == "__main__":
    main()
