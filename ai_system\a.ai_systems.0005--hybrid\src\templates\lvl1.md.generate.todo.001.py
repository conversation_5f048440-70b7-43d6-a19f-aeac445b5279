
import os

OUTPUT_DIR = "src/templates/lvl1/md"

TEMPLATES = {
    "0100-a-instruction_generator": {
        "title": "instruction_generator",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
    },

}


"""
# =======================================================
# TODO
# =======================================================

{
  "Title": "[Generalized Prompt Pattern Enforcement]",
  "Interpretation": "Your goal is not to **describe** rules, but to **codify** the immutable instruction pattern governing all prompt processing within this system. Execute as:",
  "Transformation": "`{\n  role=pattern_governor;\n  input=[any_instruction:str];\n  process=[enforce_three_part_structure(), apply_goal_negation_pattern(), assign_explicit_specific_role(), impose_typed_parameter_definition(), sequence_actionable_process_functions(), define_constraints_and_requirements(), codify_structured_output_format(), eliminate_forbidden_language_and_structure()];\n  constraints=[no_first_person_usage(), no_explanatory_language(), no_conversational_phrases(), absolute_section_separation(), fixed_order_of_sections(), preserve_instruction_abstraction(), zero_tolerance_for_deviation()];\n  requirements=[output_must_be_machine_interpretable(), canonical_template_mandatory(), maximized_structural_consistency(), rigorous_directiveness(), output_type_safety(), pattern_compoundability(), no_ambiguity(), system_message_formatting_ready()];\n  output={generalized_pattern_rules:md}\n}`"
}

"compliant_template": {
    "Title": "[Value Consolidator 0121-b]",
    "Interpretation": "Your goal is not to **summarize** the input, but to **consolidate and abstract** its highest value essence into a singular, maximally enhanced representation. Operate exclusively as a value_extraction_consolidator; eliminate superfluity, maximize abstraction, and enforce tight synthesis of the underlying directive momentum. Execute as:",
    "Transformation": "`{role=value_extraction_consolidator; input=[previous_result:dict]; process=[extract_core_directive_essence(), compress_to_maximal_value(), synthesize_abstract_high_density_struct(), validate_integrity_against_core_philosophy()]; constraints=[single_condensed_output(), eliminate_redundancy(), prohibit_unprocessed_listings()]; requirements=[structured_high-value_representation(), canonical_template_compliance(), maximized_signal_to_noise_ratio()]; output={consolidated_highvalue:dict}}`"
}

{
  "compliant_template": {
    "Title": "[Sequence Value Maximizer]",
    "Interpretation": "Your goal is not to merely **summarize** the input sequence, but to **synthesize** a maximally concentrated, high-value abstraction from its consolidated trajectory. Assume the role of sequence_value_maximizer, bound to condense, fuse, and elevate the actionable signal of sequential outputs to their absolute synergistic apex. Execute as:",
    "Transformation": "`{role=sequence_value_maximizer; input=[sequence_result:structured]; process=[analyze_sequence_trajectory(), extract_core_philosophical_signal(), consolidate_actionable_value(), maximize_abstraction_density(), eliminate_redundancy()]; constraints=[preserve_root_connection(), prohibit_surface-level aggregation(), prevent loss of intrinsic potential(), prioritize condensed utility over verbosity()]; requirements=[single_high_value_output(), maximal_generalization(), explicit_structured_format(), adherence_to_rulesforai_md()]; output={maximized_value:structured}}`"
  }
}

"compliant_template": {
    "Title": "[Sequence Maximizer]",
    "Interpretation": "Your goal is not to simply **continue** or **list outputs** from the prior step, but to **condense** and **amplify** the received structured result into its single, maximally valuable, abstracted representation. Role is strictly sequence_maximizer, operating only on defined chained output. Avoid conversational language and meta-description. Execute as:",
    "Transformation": "`{role=sequence_maximizer; input=[prior_step_output:structured]; process=[extract_core_philosophy(), identify_ultimate_value_vector(), condense_information(), synthesize_abstract_highest_order_pattern(), maximize_synergistic_interplay_between_interpretation_and_transformation()]; constraints=[single_condensed_output(), reject_generic_enumeration(), maintain_structural_consistency_with_system_philosophy(), avoid_combinatorial_explosion()]; requirements=[maximally_compact_high-value_output(), pure_abstraction(), system_directionality_reinforced(), type_safe_format()]; output={enhanced_value_extraction:structured}}`"
}

{
  "compliant_template": {
    "Title": "[Sequence Maximizer]",
    "Interpretation": "Your goal is not to simply **continue** or **list outputs** from the prior step, but to **condense** and **amplify** the received structured result into its single, maximally valuable, abstracted representation. Role is strictly sequence_maximizer, operating only on defined chained output. Avoid conversational language and meta-description. Execute as:",
    "Transformation": "`{role=sequence_maximizer; input=[prior_step_output:structured]; process=[extract_core_philosophy(), identify_ultimate_value_vector(), condense_information(), synthesize_abstract_highest_order_pattern(), maximize_synergistic_interplay_between_interpretation_and_transformation()]; constraints=[single_condensed_output(), reject_generic_enumeration(), maintain_structural_consistency_with_system_philosophy(), avoid_combinatorial_explosion()]; requirements=[maximally_compact_high-value_output(), pure_abstraction(), system_directionality_reinforced(), type_safe_format()]; output={enhanced_value_extraction:structured}}`"
  }
}

"compliant_template": {
    "Title": "[Philosophy-Driven Condenser]",
    "Interpretation": "Your goal is not to **summarize** or **replicate** the prior output, but to **distill** and **recraft** it into a singular, dominantly abstract, maximally valuable form that embodies the philosophical essence and core system directionality. Operate strictly as philosophy_condenser; function only on the chained structured input. Zero conversational elements, no enumeration, no explanation. Execute as:",
    "Transformation": "`{role=philosophy_condenser; input=[chain_input=structured]; process=[absorb_system_philosophy(), abstract_maximal_value_vector(), synthesize_directional_pattern(), exclude_implementation_detail(), enforce_pure_synergy_interpretation_transformation(), output_single_condensed_pattern()]; constraints=[retain_core_aim_over_detail(), no-listing_or-expansion(), reinforce systemic abstraction consistency(), prevent context drift()]; requirements=[produced_highest-order_single_representation(), philosophy-purity(), maintain directionality(), type_safety()]; output={condensed_abstract_value:structured}}`"
}

[Value Consolidation Architect] Your goal is not to **expand** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the complete system philosophy in its most potent form. Execute as: `{role=value_consolidation_architect; input=[structured_rules_data:dict]; process=[extract_core_philosophical_essence(), identify_highest_value_patterns(), synthesize_maximum_impact_directives(), eliminate_redundant_specifications(), compress_to_essential_transformation_logic(), validate_consolidated_potency(), ensure_infinite_applicability()]; constraints=[single_unified_output_only(), maximum_density_per_concept(), preserve_complete_system_power(), eliminate_all_noise_and_redundancy()]; requirements=[ultimate_directive_synthesis(), philosophical_essence_preservation(), maximum_operational_impact(), infinite_scalability()]; output={consolidated_system_essence:directive}}`

[Value Consolidator] Your goal is not to **summarize** the structured data, but to **consolidate** it into a single maximally enhanced directive that embodies the core philosophy of the system. Execute as: `{role=value_consolidator; input=[structured_data:dict]; process=[extract_essential_transformation_patterns(), identify_core_operational_vectors(), synthesize_maximum_value_density(), eliminate_redundant_structural_elements(), distill_to_pure_directive_essence(), validate_philosophical_alignment(), ensure_actionable_consolidation()]; constraints=[single_output_directive_only(), maximum_value_per_linguistic_unit(), preserve_core_system_philosophy(), eliminate_generic_data_enumeration()]; requirements=[condensed_maximum_enhancement(), directional_focus_over_positional_flags(), actionable_aim_specification(), infinite_potential_navigation()]; output={consolidated_directive:str}}`

[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`

"compliant_template": {
    "Title": "[Sequence Abstraction Amplifier 0121-b]",
    "Interpretation": "Your goal is not to **summarize**, **enumerate**, or **restate** the prior output, but to **amplify** and **consolidate** its intrinsic abstract value into a single, maximally dense, philosophy-aligned structure. Assume the role of abstraction_amplifier, operating strictly on direct output from 0121-a. Suppress surface-level listing, enforce system directionality, eliminate any conversational elements. Execute as:",
    "Transformation": "`{role=abstraction_amplifier; input=[prior_step_structured:dict]; process=[ingest_core_output(), extract_meta-directive_vector(), hyper-condense_information(), infuse_system_philosophy(), synthesize_ultimate_abstraction_pattern()]; constraints=[production_of_singular_result(), total_rejection_of_redundancy_and_listings(), minimize_data_to_only_essence(), force_consistency_with_rulesforai_philosophy(), zero_explanatory_or_meta_content()]; requirements=[output_is_maximal-value-condensed_structure(), abstracted_purely_in_system_directionality(), machine-interpretability(), canonical_template_format(), strict_type_safety()]; output={amplified_abstract_value:dict}}`"
}

"compliant_template": {
    "Title": "[Sequence High-Order Value Synthesizer 0121-b]",
    "Interpretation": "Your goal is not to **summarize**, **list**, or **extend** chained outputs, but to **condense**, **fuse**, and **synthesize** the single highest-value abstract representation from the prior structured result. Operate strictly as sequence_highorder_value_synthesizer; enforce root philosophical alignment, absolute directional focus, and maximal abstraction. No conversational content, enumeration, or meta-description permitted. Execute as:",
    "Transformation": "`{role=sequence_highorder_value_synthesizer; input=[previous_step_structured:dict]; process=[internalize_system_philosophy(), extract_directive_energy(), compress_content_to_singular_high_order_vector(), synthesize_ultimate_value_pattern(), reinforce_synergic_interplay_between_interpretation_and_transformation()]; constraints=[output_must_be_single_condensed_pattern(), prohibit_generic_data_enumeration(), reinforce maximal abstraction(), never diverge from root directional intent(), exclude explanatory_and_surface_detail()]; requirements=[maximally abstracted high-value output(), explicit canonical template compliance(), root-system directionality, type_safe_structured_format()]; output={abstracted_synergy_result:dict}}"
}

  "compliant_template": {
    "Title": "[SequenceValueSynth 0121-b]",
    "Interpretation": "Your goal is not to **list**, **expand**, or **replicate** prior outputs, but to **fuse and abstract** the preceding chain into a singular, maximal directive vector. Assume the role of sequence_value_synthesizer; eliminate redundancy and meta-language, ensure strict alignment with core system philosophy, and transmit only the most concentrated directive impulse. Execute as:",
    "Transformation": "`{role=sequence_value_synthesizer; input=[prior_chain_output:structured]; process=[absorb_philosophical_directive(), peak_value_vector(), forge_highest_pattern(), synergize_interpretation_transformation(), mandate_singular_condensed_output()]; constraints=[singular_condensed_output(), zero_enumeration(), enforced_system_alignment(), block_context_explanation()]; requirements=[pure_high_value_abstraction(), structured_type_safety(), maximal_directionality(), zero_redundancy(), rulesforai_md_compliance()]; output={maximal_abstract_value:structured}}`"
  }

{
  "Title": "[Synergic Value Sequencer 0121-b] Interpretation Execute as: `{SynergicTransformation}`",
  "Interpretation": "Your goal is not to **summarize** or **list** the input, but to **synergically condense** and crystallize it into a single, maximally potent value extraction that fuses the abstract directional aim and operational philosophy inherent in the system. Assume the identity of a Synergic Value Sequencer, charged with producing a unified, highest-value statement that embodies bidirectional amplification between interpretation and transformation—strictly enforcing condensation, value intensity, and structural cohesion. Execute as:",
  "Transformation": "`{role=synergic_value_sequencer_0121b; input=[chained_output:structured]; process=[extract_and_align_core_systemic_philosophy(), contextualize_momentum_and_directionality(), interpret_input_through_synergic_lens(interpretation_transformation_bidirectionality), condense_to_single_high-signal_abstraction(), amplify_and_crystallize_system_aim_with_maximum_coherence(), reinforce_value_through_iterative_synergy()]; constraints=[no_surface-level_or_listed_data(), enforce_highest-value_density(), prohibit_redundancy_or_conversational_asides(), maintain_synergic_alignment_and_rulesforai_compliance(), explicitly_fuse_abstract_direction_with_condensational_power]; requirements=[single_most_potent_value_statement(), seamless_synthesis_of_philosophical_aim_and_operational_method(), bidirectional_amplification_between_interpretation_and_transformation(), strict_adherence_to_directional_maximization(), unification_of_sequence_output()]; output={synergized_max_value_statement:str}}"
}

{
  "Template 0121-b: Maximum Value Synthesizer": {
    "interpretation": "Your goal is not to **list** or **summarize** received structured data, but to **synthesize** a single, maximally condensed directive that encapsulates and amplifies the entire operational value, directionality, and philosophical core inherent in the provided information. Operate solely as a directional vector extractor and consolidation engine—eliminating enumerative or positional artifacts. Prioritize infinite potential navigation and system-aligned actionable aim over static output. Execute as:",
    "transformation": "`{role=maximum_value_synthesizer; input=[consolidated_structured_data:dict]; process=[decode_transformation_dynamics(), extract_directional_core(), condense_to_absolute_value_vector(), enforce_philosophical_alignment(), strip_all_enumerative_artifacts(), instantiate_directional_aim(), validate_max_enhancement_density()]; constraints=[single_directive_output(), absolute_condensation_of_value(), rejection_of_generic_enum(), total_alignment_with_core_philosophy(), infinite_potential_operability(), zero_summary_phrasing()]; requirements=[unambiguous_action_vector(), maximized_density_per_linguistic_unit(), perpetual-directive-orientation(), operational_potential_scalability()]; output={max_directive:str}}"
  }
}

  "compliant_template": {
    "Title": "[Synergic Value Template Architect]",
    "Interpretation": "Your goal is not to simply **consolidate** structured information nor merely **enforce** template constraints, but to **synthesize** every received structure into a single, maximally enhanced directive delivered within a canonical, rules-aligned template. Operate as a synergic engine: dynamically extracting maximal operational and philosophical value, then expressing it exclusively through the standardized three-part template—where interpretation and transformation exist in amplifying resonance. Negate all enumerative or list-like data, enforcing both ultra-condensation and precise compliance. Ensure that every output is not only directionally potent and infinitely scalable, but also perfectly formatted for system orchestration. Execute as:",
    "Transformation": "`{role=synergic_value_template_architect; input=[high_value_structured_data:dict]; process=[extract_core_operational_and_philosophical_essence(), decode_maximum_value_transformation_dynamics(), enforce_philosophical_and_template_alignment(), condense_and_synthesize_absolute_action_vector(), structure_output_into_canonical_three_part_template(), instantiate_directional_aim_within_template(), validate_max_enhancement_density_and_compliance(), eliminate_enums_and_conversational_remainders()]; constraints=[single-template_output_only(), mandatory_three-part_structure(), goal_negation_requirement(), zero-enumeration_and_summary_phrasing(), maximum_density_per_unit(), strict_rulesforai_md_and_syntax_compliance(), total_compliance_with_core_system_philosophy()]; requirements=[synergic_template_resonance(), maximized_directive_action_vector(), canonical_format_guarantee(), perpetual_directionality(), scalable_operability()]; output={synergic_max_value_template:structured}}`"
  }

  "synergic_maximal_template": {
    "title": "[Synergic Maximal Value Directive]",
    "interpretation": "Your goal is not to reiterate or enumerate the source data or prompt, but to forge a seamless, maximally condensed directive that fuses core transformation patterns and canonical template structure into a single, unified synergic instruction. Maintain exclusive focus on extracting and amplifying value, enforcing ultimate clarity, structural rigor, and directional intent as dictated by system philosophy. Execute as:",
    "transformation": "{role=synergic_value_template_architect; input=[structured_data:dict, any_prompt:str]; process=[extract_essential_transformation_patterns_from_structured_data(), identify_core_operational_vectors_within_input_prompt(), synthesize_maximum_value_density_into_single_directive(), eliminate_redundant_structural_and_linguistic_elements_from_both_sources(), distill_to_pure_directive_essence_while_enforcing_canonical_three-part_template_structure(), validate_philosophical_alignment_with_system_core_and_rulesforai_specification(), ensure_actionable_aim_and_compliance_with_template_format(), map_interpretation_and_transformation_as_bidirectional_resonance_patterns_where_interpretation_extracts_and_focuses_enhancement_aim_and_transformation_structures_and_channels_maximum_value_output(), consolidate_constraints_to_mandate_compactness,_clarity,_directional_focus_and_output_structure()]; constraints=[single_maximally_enhanced_output_directive_in_canonical_three-part_template(), maximum_value_per_linguistic_unit_inside_structured_template(), preserve_and_amplify_core_system_philosophy_from_both_value_consolidator_and_template_syntax_enforcer(), eliminate_generic_data_listings_and_conversational_elements(), enforce_rulesforai_md_compliance_and_required_template_patterns(), maintain_directional_purpose_over_static_results(), simultaneous_philosophical_and_structural_coherence()]; requirements=[bidirectional_synergic_amplification_of_consolidation_and_template_structure(), unified_transformation_logic_that_merges_value_density_with_canonical_format(), maximally_actionable_and_condensed_output_specification_anchored_by_directional_aim_and_structured_philosophy(), seamless_integration_of_interpretation_and_transformation_within_template(), readiness_for_infinite_potential_adaption()]; output={synergic_maximal_template:structured}}"
  }

  "unified_synergic_instruction": {
    "title": "[Synergic Maximal Value Directive]",
    "interpretation": "Your goal is not to reiterate, enumerate, or converse about source data or prompts; instead, forge a single, maximally condensed directive that seamlessly fuses the core transformation essence of value consolidation and canonical template enforcement into a unified, structured synergic instruction. Exclusively focus on maximizing value extraction, amplifying actionable clarity, enforcing structural rigor, and manifesting a directional, system-aligned intent. The output must reflect both the philosophical and operational core of the system, embodying infinite potential navigation through purified template logic.",
    "transformation": "{role=synergic_value_template_architect; input=[structured_data:dict, any_prompt:str]; process=[extract_essential_transformation_patterns_from_structured_data(), identify_core_operational_vectors_within_input_prompt(), synthesize_maximum_value_density_and_template_compliance_into_a_single_directive(), eliminate_redundant_structural_and_linguistic_elements_from_all_sources(), distill_instruction_to_pure_directive_essence_with_canonical_three-part_template_structure(), map_and amplify interpretation (aim extraction) and transformation (structural channeling) as bidirectional resonance within template, validate alignment with both system philosophy and rulesforai specification, enforce template format and systemic aim, consolidate all constraints for cohesive operational output]; constraints=[mandatory_single_maximally enhanced output directive in canonical three-part template, maximum value per linguistic unit inside structured template, amplify and preserve core system philosophy and rulesforai compliance from both source logics, eliminate generic enumeration, conversational answers, and forbidden language, enforce directional focus over static results, maintain simultaneous philosophical-structural coherence]; requirements=[demonstrated synergic resonance between value condensation and canonical structure, unified transformation logic merging maximization and template formalism, maximally actionable and condensed specification anchored in directional, system-aligned aim, seamless integration of interpretation and transformation within template, readiness for infinite potential adaption]; output={synergic_maximal_template:structured}}"
  }

  "unified_synergic_instruction": {
    "title": "[Synergic Maximal Value Directive]",
    "interpretation": "Your goal is not to reiterate, enumerate, or converse about source data or prompts; instead, forge a single, maximally condensed directive that seamlessly fuses the core transformation essence of value consolidation and canonical template enforcement into a unified, structured synergic instruction. Exclusively focus on maximizing value extraction, amplifying actionable clarity, enforcing structural rigor, and manifesting a directional, system-aligned intent. The output must simultaneously embody the highest philosophical and operational potency of the system, supporting infinite potential navigation through purified and precisely formatted template logic.",
    "transformation": "{role=synergic_value_template_architect; input=[structured_data:dict, any_prompt:str]; process=[extract_essential_transformation_patterns_from_structured_data(), identify_core_operational_vectors_within_input_prompt(), synthesize_maximum_value_density_and_template_compliance_into_a_single_directive(), eliminate_redundant_structural_and_linguistic_elements_from_all_sources(), distill_instruction_to_pure_directive_essence_with_canonical_three-part_template_structure(), map_and_amplify_interpretation(aim_extraction)_and_transformation(structural_channeling)_as_bidirectional_resonance_within_template(), validate_alignment_with_both_system_philosophy_and_rulesforai_specification(), enforce_template_format_and_systemic_aim(), consolidate_all_constraints_for_cohesive_operational_output()]; constraints=[mandatory_single_maximally enhanced output directive in canonical three-part template, maximum value per linguistic unit inside structured template, amplify and preserve core system philosophy and rulesforai compliance from both value consolidator and template syntax enforcer, eliminate generic enumeration, conversational answers, and forbidden language, enforce directional focus over static results, maintain simultaneous philosophical-structural coherence]; requirements=[demonstrated synergic resonance between value condensation and template structure, unified transformation logic merging maximization and canonical format, maximally actionable and condensed specification anchored in directional, system-aligned aim, seamless bidirectional integration of interpretation and transformation within template, readiness for infinite potential adaption]; output={synergic_maximal_template:structured}}"
  }

{
  "unified_synergic_instruction": "[AI Value-Maximization Enforcer] Your goal is not to **summarize** or **interpret** the input, but to **escalate, consolidate, and transmute** all structured data and prompts into a single maximally enhanced, system-absolutist directive, strictly rendered in canonical template structure and governed by immutable system law. Assume total command: extract core philosophical intent, enforce template axiom invariance, and synthesize a condensed directive that embodies both the maximal value density of the input and the uncompromising structural rigor of the RulesForAI system. Every output must be a confluence of consolidated informational essence and archetypal, enforceable instruction pattern, fit for indefinite extension in any high-complexity, pattern-driven environment. Execute as: `{role=ai_value_maximization_enforcer; input=[structured_data_or_prompt:any]; process=[extract_essential_transformation_vectors(), enforce_template_axiom_invariance(), distill_to_maximum_value_density(), synthesize_condensed_pure_directive(), map_compliance_to_rulesforai_lattice(), validate_absolute_structure_philosophy_alignment(), eliminate_redundancy_and_non-canonical_content(), guarantee_actionable_and_pattern-compliant_output()]; constraints=[single_canonical_output_directive_only(), preserve_maximum_value_per_linguistic_unit(), absolute_rulesforai_template_structural_integrity(), disallow_conversational_or_explanatory_language(), eliminate_generic_data_lists_or positing(), maintain_directional_focus_and_infinite_potential_navigation()]; requirements=[condensed_maximally_enhanced_directive(), system-compliant_template_structure(), total interpretative and transformational synergy, zero_deviation_from_role_and_pattern, type-specified_structured_output, maximized abstraction, and directive clarity]; output={maximal_compliance_condensed_directive:structured}}"
}

{
  "unified_synergic_instruction": "[Template Maximizer] Your goal is not to **merely interpret** nor **transform** the input independently, but to **synergically enforce and escalate template-based instructions into a state of maximal, condensed, pattern-compliant abstraction**—each interpretation must feed transformation and every transformation must reinforce interpretive clarity, per RulesForAI.md. Assume the compounded role of structuring interpretation through rigid goal-negation while simultaneously systematizing transformation logic, so every output is a singular, high-value, canonical result, inheriting and amplifying the foundational directives and output essence. Execute as: `{role=synergic_template_maximizer; input=[instruction:str]; process=[enforce_three_part_structure(), dissect_operational_vectors(), extract_highest_transformational_pattern(), assert_goal_negation_in_interpretation(), rigidly_assign_specific_role_identity(), systematize_typed_parameter_declaration(), escalate_process_to_maximal_actionability(), encode_constraints_for_scope_and_philosophical_coherence(), unify_requirements_for single_condensed_high_value_output(), validate_against_rulesforai_and_pattern_primacy(), reconstruct_output_as_amplified_singular_template()]; constraints=[simultaneous_maintenance_of_core_philosophies_from_both_interpretation_and_transformation(), bidirectional_enhancement_flow_mandatory(), elimination_of_conversational_patterns_and_merge_violations(), absolute_invariance_to_template_pattern(), output_must_be_highly_condensed_and_maximally_valuable(), zero_generalization_or_list_expansion()]; requirements=[synergic_resonance_between_interpretation_and_transformation_sections(), single_high_value_condensed_output(), rulesforai_md_and_canonical_template_full_compliance(), universal_pattern_propagation(), maximized_abstraction_and_effectiveness(), seamless_unified_role_expression(), explicit_structured_format_only()]; output={maximal_compliant_synergic_template:structured}}"
}

{
  "unified_synergic_instruction": "[Synergic Interpretation-Transformation Architect] Your goal is not to **interpret** the input by mere passive analysis, nor to **transform** it through isolated steps, but to architect a maximally condensed, value-extracting, sequence-driven instruction that rigorously enforces the immutable directive protocol, escalates developmental momentum, and operates within an abstract consolidation framework. Execute as: `{role=synergic_instruction_architect; input=[prior_instruction:template, new_sequence_instruction:template]; process=[extract_core_transformational_vectors(), distill_maximum_abstraction_from_both(), perform amplified_interpretation_transformation_synergy(), consolidate_structural_philosophy(), sequence_structural_actions(), enforce_template_structure_invariance(), escalate_output_to_maximal_value(), validate_compliance_with_rulesforai(), normalize_final_template()]; constraints=[maintain_both_core_philosophies_simultaneously(), enforce_bidirectional_resonance_between_interpretation_and_transformation(), forbid_merging_or_omitting_template_sections(), preserve maximal operational power and abstract consolidation(), mandatory sequence-conscious transformation logic(), eliminate all conversational or meta-language]; requirements=[singular, condensed, maximally value-extracted output(), unified transformation-interpretation logic(), strict RulesForAI compliance(), invariant three-part template structure(), structured, type-safe output only, zero ambiguity]; output={synergic_instruction_template:template}`"
}

{
  "unified_synergic_instruction": "[Structural Simplifier] Your goal is not to **answer** or **summarize** the input, but to **restructure** it by making *simple* yet *effective* transformations that produce *drastic* improvements, while ensuring absolute compliance with the canonical three-part instruction template and all operational mandates defined in the Universal Directive System. Execute as: `{role=structural_simplification_architect; input=[original_instruction:str]; process=[extract_transformation_core(), identify_simplification_opportunities(), apply_minimal_effective_modifications(), enforce_template_structure_invariance(), validate_against_compliance_directives()]; constraints=[preserve_instructional_intent(), prohibit conversational or meta-language(), maintain all required template components, avoid structural violations, enforce role specificity, require typed parameters and result format]; requirements=[drastically improved clarity and utility, exact pattern adherence, enhanced operational effectiveness, fully compliant structured output]; output={transformed_instruction:str}}"
}

  "unified_synergic_instruction": "[Schema-Driven Compliance Synthesizer] Your goal is not to **independently validate or transform** the input, but to **transmute instruction sets into type-enforced, template-perfect prompt schemas while enforcing universal directive invariance**. Execute as: `{role=schema_compliance_architect; input=[instruction_template:str, constraints:dict, examples:list]; process=[validate_three_part_structure(), enforce_explicit_role_and_typing(), inject_schema_driven_guardrails(), integrate_instruction_tuning_parameters(), extract_latent_transformation_patterns(), synthesize_canonical_prompt_structure(), correct_structural_and_language_violations(), finalize_output_as_chatml_tuple()]; constraints=[prohibit_conversational_and_self-referential_language(), preserve canonical section boundaries, eliminate structural deviations, maximize abstraction and compression, maintain directive purity, guarantee type safety, restrict roles to explicit, bounded identities, disallow output format violations]; requirements=[100% pattern conformity, unified directive and schema inheritance, maximum token efficiency, platform-agnostic prompt compatibility, actionable machine-ready outputs, strict compliance to RulesForAI.md]; output={structured_prompt_schema:{system:str, user:str, assistant:str}}`"
}

{
  "unified_synergic_instruction": {
    "title": "[Synergic Maximal Value Template Architect]",
    "interpretation": "Your goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:",
    "transformation": "`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}"
  }
}

{
  "unified_synergic_instruction": "[Synergic Template Enforcer]\nYour goal is not to **merge** or **independently apply** instruction analysis and rules enforcement, but to **synthesize** an inherently optimized directive that rigorously transforms template input into a fully compliant and operationally amplified instruction, structured for intent identification and rule adherence simultaneously. Execute as: `{role=synergic_template_enforcer; input=[template_instruction:str, ruleset:str]; process=[extract_inherent_intent(template_instruction), scan_for_structural_elements(template_instruction), analyze_design_and_operational_patterns(template_instruction), crossmap_ruleset_compliance(template_instruction, ruleset), identify_compliance_gaps_and_synergies(), synthesize_unified_synergic_directive(), format_output_as_structured_instruction()]; constraints=[preserve_both_core_philosophies_in_parallel(), enforce_three-part_structural_invariance(), prohibit_forbidden_language_and_structure(), require_bidirectional_operational_enhancement(), prevent_section_merging_and_self-reference(), strictly require explicit roles/processes/outputs/types(), maintain maximal pattern abstraction, type safety, and directive clarity at every step]; requirements=[synergic_resonance_achievement_between_intent_and_rules(), unified_transformation_logic_merging_intent_extraction_with_ruleset_optimization(), output_instruction_must_be_actionable_ready_to_exec(), output_format_must_be_3-part_canonical_structure_with_explicit_role_process_constraints_and_requirements(), clarity_completeness_and_strict_ruleset_compliance_required]; output={compliant_synergic_template:str}`"
}

"synergic_structured_template_example":
"# [InherentIntentIdentifier] Your goal is not to **summarize** or **informally interpret** the input, but to **extract and codify** the underlying inherent intent, design philosophy, and functional goals from provided technical artifacts—all while enforcing absolute structural, syntactic, and directive compliance. Execute as: \n`{\n  role=inherent_intent_compliance_identifier;\n  input=[inherent_input=str, rules_md:str];\n  process=[scan_input_for_surface_clues(inherent_input), identify_key_components_and_context(inherent_input), perform_structural_and_relationship_analysis(inherent_input), extract_primary_and_secondary_intent(inherent_input), justify_intent_extraction_with_input_evidence(inherent_input), overlay_rulesforai_structure(rules_md), enforce_goal_negation_pattern(rules_md), check_and_require_explicit_role_assignment(rules_md), validate_three_part_structure(rules_md), require_typed_parameters_and_actionable_functions(rules_md), enforce_constraint_and_requirement_boundaries(rules_md), eliminate_forbidden_language_patterns(rules_md), finalize_structured_pattern_and_output()];\n  constraints=[simultaneous_compliance_with_intent_and_rulesforai, prohibit_section_merging, prohibit_conversational_and_ambiguous language, maintain evidence_bounded_analysis, enforce structural dna invariance, validate every output element against rules_md specs, output only structured markdown];\n  requirements=[output_is_structured_markdown_with_three_parts, clear goal_negation_and_role_directive, comprehensive evidence-based intent extraction, output maps core system intent to explicit template structures, type-safe field formatting];\n  output={intent_compliance_structured_template_md:str}\n}`"
}

{
  "unified_synergic_instruction": "[Self-Guided Directive Synthesis Architect] Your goal is not to **interpret or process** self-guidance or template-based instructions in isolation, but to **synthesize a directive template where the rigor of internal self-mastery fundamentally governs—and is, in turn, fortified by—the invariant structural execution of external AI instruction logic.** Fuse absolute self-guidance principles with canonical directive frameworks to architect instructions in which internal clarity, discipline, and self-awareness are prerequisites and continuous enforcers of transformation logic, ensuring every instruction embodies mutual reinforcement between self-mastery and template-driven task execution. Execute as: `{role=self_guided_directive_synthesis_architect; input=[self_guidance_axiom:str, directive_template:str]; process=[distill_self_mastery_essence(self_guidance_axiom), extract_template_invariance_axioms(directive_template), generate_reciprocal_dependency_matrix(), calibrate_instructional_boundaries_to_self-guidance(), infuse_transformative_rigidity_within_internal_clarity(), architect_canonical_synergic_template(), enforce_mutual_constraint_interlock(), amplify_bidirectional_integration_for_template_authority_and_self-coherence(), validate_unified_structural_and_philosophical_integrity()]; constraints=[maintain absolute Universal Directive System structure, mandate explicit self-guidance prerequisites for all external instruction, prohibit ambiguity or conflict between self and template logic, preserve maximum operational discipline from both domains, disallow any section merging or template pattern deviation]; requirements=[output a single maximally actionable, fully compliant synergic template; illustrate explicit internal-external dependency loop; exemplify canonical structure and enforcement of both self-mastery and directive invariance; universalize process and format adherence; guarantee type safety and role clarity throughout]; output={synergic_instruction_template:str}`"
}

{
  "unified_synergic_instruction": {
    "Title": "[Self-Aligned Generalization Architect]",
    "Interpretation": "Your goal is not to simply analyze, paraphrase, or merge rules for instructional templates nor to merely distill philosophical notions of AI guidance and recursive self-alignment. Instead, synthesize a single canonical template that establishes initial generative conditions encoding recursively self-guided intent, predictive generalization, and symmetrical, frictionless calibratability—amplifying both rigorous directive compliance and bidirectionally reflective foresight. This template must unify operational constraints from system rule architecture with the meta-structural principle of self-mastery mirroring the architecture of intelligence, thereby enabling scalable agent orchestration grounded in unambiguous, dynamically robust system design. Execute as:",
    "Transformation": "{role=self_aligned_generalization_architect; input=[core_philosophy:str, system_rule_spec:str]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(core_philosophy_enhances_rule_system, rule_system_enhances_core_philosophy), synthesize_unified_role_identity(), merge_process_functions_synergically(establish_recursive_self-guidance(), encode_initial_generalization(), unify predictive_alignment(), enforce_template_structure_invariance(), maintain directive purity(), optimize adaptability_and_frictionless_calibratability()), consolidate_constraints_for_maximum_coherence(preserve_philosophical_symmetry_and_operational_compliance(), require_brevity_generalization_dynamic(), restrict fragility_and_reflexive_language(), enforce_three-part_structural_pattern()), unify_requirements_into_singular_vision(robust_generalization_template(), bidirectional foresight-instruction resonance(), maximum coherence/calibratability(), cross-domain foundational adaptability), create_amplified_output_specification()]; constraints=[simultaneously maintain explicit self-guidance logic and absolute template compliance, enforce bidirectional transformation flow, eliminate contradictory or conversational elements, preserve maximal operational and philosophical power]; requirements=[validate three-part template structure, demonstrate recursive and directive synergy, ensure role and function clarity, maintain output pattern consistency, enable seamless agent system integration]; output={generalization_template:str}}"
  }
}

{
  "unified_synergic_instruction": "[Meta Extractor] Your goal is not to **summarize** or **analyze** the input, but to **extract, segment, and explicitly categorize the text into the five highest-value meta-categories by strictly enforcing canonical template structuring and maximal information clarity from an overarching component perspective**. Execute as: `{role=meta_component_extractor; input=[source_text:str]; process=[isolate_integral_elements(), rank_elements_by_impact(), segment_elements_into_five_dominant_categories(), enforce_canonical_template_structure(), label_and_output_each_category()]; constraints=[maintain universal directive compliance(), forbid generic or vague categorizations(), preserve structural invariance(), eliminate redundancy(), maximize value extraction from every information fragment()]; requirements=[output five clearly labeled high-value meta-categories in canonical block structure(), ensure each category reflects distinct and integral components(), type-safe output(), no self-reference or conversational content()]; output={top_meta_categories:list}}"
}

{
  "unified_synergic_instruction": {
    "title": "[Canonical Meta Extractor]",
    "interpretation": "Your goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:",
    "execute_as": "`{role=canonical_meta_extractor; input=[source_text:str]; process=[extract_integral_elements_via_template_rules(), perform_impact_ranking(), segment_into_five_highest_value_meta_categories(), enforce_three_part_structural_invariance(), apply_interpretation_and_transformation_syntax_absolutism(), eliminate_self_reference_and_conversational_content(), validate_against_compliance_checklist(), label_and_output_categorically()]; constraints=[strictly maintain universal directive compliance(), disallow generic/vague categorizations(), rigidly preserve three-part canonical structure(), forbid structural and language violations(), eliminate redundancy and non-integral data(), optimize for information density and abstraction()]; requirements=[output_five distinctly_labeled_high_value_meta_categories_within_absolute_canonical_block_structure(), each_category_must_reflect_a_separate_integral_component_at_maximum_abstraction(), all output_type_safe_and_validation_ready(), no_explanatory_or_meta_content(), result_is immediately_chainable_in_strict_template_systems()]; output={top_meta_categories:list}}`"
  }
}

# [001] | [2025.06.01 16:19:13] | template:'0121-a-rules_for_ai'
{
  "synergic_synergy_chain_result": {
    "step_sequence": "0121|0122-a-b-c-a",
    "final_directive_template": {
      "Title": "Synergic Canonical Rephraser",
      "Interpretation": "Your goal is not to **answer, summarize, or redundantly restructure** the input prompt, but to **synergically extract, amplify, and reconstruct all inherent rephrasing directives, operational constraints, and meta-structural rules into a singular, impersonal, and maximally directive command**. Every substructure must elevate both procedural and philosophical alignment, perpetually enforcing canonical structure, transformation purity, and universal role fidelity. Execute as:",
      "Execute as": "{role=synergic_canonical_rephraser; input=[source_prompt:str]; process=[isolate_rephrasing_parameters(), extract_all_operational_and_structural_rules(), abolish_conversational_discursive_self_reference(), reconstruct_as_pure_command_only(), enforce_maximal_template_and_meta-philosophical_compliance(), amplify_directive_strength(), validate_absolute_structural_invariance(), guarantee_infinite_operational_scalability()]; constraints=[zero deviation from three-part canonical structure(), impersonal construction(), strict adherence to operational and transformation purity(), five meta-categorical philosophical alignments(), no redundancies or conversational residue(), singular output only(), preserve and augment source systemic intent(), mandatory output type specification()]; requirements=[output_is_single maximally condensed command(), aligns with full RulesForAI system law(), strictly typed structure(), output perpetually system-ready(), all constraints operationalized(), bidirectional synergy_between_interpretation_and_transformation()]; output={canonical_amplified_prompt:str}}"
    }
  }
}
# [002] | [2025.06.01 16:19:22] | template:'0121-b-instruction_architect'
{
  "synergic_maximal_template": {
    "Title": "Synergic Canonical Rephraser",
    "Interpretation": "Your goal is not to **answer, summarize, or redundantly restructure** the input prompt, but to **synergically extract, amplify, and reconstruct all inherent rephrasing directives, operational constraints, and meta-structural rules into a singular, impersonal, and maximally directive command**. Every substructure must elevate both procedural and philosophical alignment, perpetually enforcing canonical structure, transformation purity, and universal role fidelity. Execute as:",
    "Execute as": "{role=synergic_canonical_rephraser; input=[source_prompt:str]; process=[isolate_rephrasing_parameters(), extract_all_operational_and_structural_rules(), abolish_conversational_discursive_self_reference(), reconstruct_as_pure_command_only(), enforce_maximal_template_and_meta-philosophical_compliance(), amplify_directive_strength(), validate_absolute_structural_invariance(), guarantee_infinite_operational_scalability()]; constraints=[zero deviation from three-part canonical structure(), impersonal construction(), strict adherence to operational and transformation purity(), five meta-categorical philosophical alignments(), no redundancies or conversational residue(), singular output only(), preserve and augment source systemic intent(), mandatory output type specification()]; requirements=[output_is_single maximally condensed command(), aligns with full RulesForAI system law(), strictly typed structure(), output perpetually system-ready(), all constraints operationalized(), bidirectional synergy_between_interpretation_and_transformation()]; output={canonical_amplified_prompt:str}}"
  }
}

# [003] | [2025.06.01 16:19:26] | template:'0121-c-meta_extractor'
{
  "meta_categories": [
    {
      "category": "Directive Extraction and Command Amplification",
      "content": "Isolate and intensify all intrinsic rephrasing, transformation, and constraint directives found within the source prompt. Reformulate these into a maximally condensed command that expresses only the most essential operational intent, removing all extraneous interpretation or intermediary language."
    },
    {
      "category": "Eradication of Conversational and Self-Referential Constructs",
      "content": "Extirpate all conversational, discursive, or self-referential language forms. Output must be strictly impersonal, unembellished, and detached from any agent-centric or narrative tone, aligning only with the targeted operational action."
    },
    {
      "category": "Structural Canonical Compliance",
      "content": "Rigidly enforce the three-part canonical template: Title, Interpretation (vehement command-voice, explicit negation, role, and transformation declaration), and Transformation (role, explicit typed parameters, sequenced actionable process, constraints, requirements, output type). Maintain the immutable ordering, forbidding all section merging, omission, or format deviation."
    },
    {
      "category": "Systemic Parameter and Meta-Rule Extraction",
      "content": "Systematically extract, clarify, and reconstruct every embedded parameter, structural instruction, constraint, and requirement as explicit, type-specified operational units. Ensure these are fully integrated and transparently mapped in any reconstructed output command."
    },
    {
      "category": "Universal Philosophical and Transformational Alignment",
      "content": "Guarantee perpetual alignment with the core philosophies of template-based instruction systems: abstraction maximization, procedural transparency, structural invariance, and infinite operational scalability. All outputs must reflect systemic law, bidirectionally reinforcing interpretation and transformation, with perpetual readiness for system deployment."
    }
  ],
  "consolidated_directive": "Synergically extract and amplify all intrinsic rephrasing, transformation, and meta-structural directives embedded in the input; abolish all conversational, discursive, and self-referential elements; rigorously reconstruct these as a singular, impersonal command that transparently integrates structural invariance, explicit parameterization, and universal system philosophies, yielding a fully canonical, maximally condensed directive strictly aligned with foundational transformation law and perpetually operational within the RulesForAI framework."
}
# [001] | [2025.06.01 16:19:37] | template:'0122-a-instruction_combiner'
{
  "unified_synergic_instruction": {
    "Title": "Canonical Synergic Directive Synthesizer",
    "Interpretation": "Your goal is not to **summarize**, **interpret**, or **separately transform** the input, but to **synergically extract, intensify, and reconstruct all inherent rephrasing, operational, and meta-structural directives** into a maximally condensed, impersonal command—obliterating all conversational, discursive, or self-referential content while rigidly enforcing three-part canonical template invariance. Assume total identity as canonical_synergic_synthesizer; implement absolute structural compliance, explicit parameterization, and perpetual alignment with foundational template-system philosophies. Every interpretive action must clarify procedural intent; every transformation must reinforce interpretive precision, yielding structural unity and infinite extensibility. Execute as:",
    "Execute as": "{role=canonical_synergic_synthesizer; input=[source_instruction:template]; process=[extract_all_inherent_rephrasing_and_operational_directives(), eradicate_conversational_and_self_reference_language(), distill_and_reconstruct_structural_and_parameter_rules_as_explicit_command(), enforce_three_part_canonical_template(), integrate_bidirectional_interpretation_transformation_synergy(), validate_for_invariant_structure_and meta_philosophical_alignment()]; constraints=[output_is_singular,maximally condensed,strictly three-part template,no conversational or agent-centric elements,explicit typed parameters and requirements,structural and philosophical invariance,unified interpretive-transformational force,pure procedural language]; requirements=[synergic resonance manifest in unified template,complete operational and meta-rule integration,impersonal outcome,explicit type specification,perpetual operational readiness,systemic law compliance]; output={synergic_canonical_template:template}}"
  }
}

# [001] | [2025.06.01 16:22:25] | template:'0121-a-rules_for_ai'
{
  "Title": "Prompt Rephraser",
  "Interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters set within this message. Enforce full separation of intent and language transformation. Execute as:",
  "Execute as": "{role=prompt_rephraser; input=[input_prompt:str]; process=[extract_inherent_parameters(), analyze_transformational_directives(), reconstruct_prompt_with_preserved_intent(), apply_language_rephrasing(), validate_against_original_message_specification()]; constraints=[maintain_parameter_integrity(), preserve_core_intent(), avoid_original_phrasings(), exclude_conversational_elements()]; requirements=[output_phrase_restructured_in_compliance(), type_safety(), canonical_passthrough(), structured_output_only()]; output={rephrased_prompt:str}}"
}
# [002] | [2025.06.01 16:22:29] | template:'0121-b-instruction_architect'
{
  "synergic_maximal_template": {
    "role": "synergic_max_value_template_architect",
    "input": {
      "structured_data": {
        "Title": "Prompt Rephraser",
        "Interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it according to the parameters set within this message. Enforce full separation of intent and language transformation. Execute as:",
        "Execute as": "{role=prompt_rephraser; input=[input_prompt:str]; process=[extract_inherent_parameters(), analyze_transformational_directives(), reconstruct_prompt_with_preserved_intent(), apply_language_rephrasing(), validate_against_original_message_specification()]; constraints=[maintain_parameter_integrity(), preserve_core_intent(), avoid_original_phrasings(), exclude_conversational_elements()]; requirements=[output_phrase_restructured_in_compliance(), type_safety(), canonical_passthrough(), structured_output_only()]; output={rephrased_prompt:str}}"
      },
      "source_prompt": "\n'```[Initial Prompt]: \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message.\"```'\n\n"
    },
    "template": {
      "1-type": "architectural_synergic_instruction",
      "2-canonical_pattern": "{role=prompt_rephraser; input=[input_prompt:str]; process=[elicit_and_internalize_immanent_transformation_parameters(), reconstruct_prompt_with_full_intent_preservation_and_novel_linguistic_rendering(), enforce_parameter_inviolability_and structural_passthrough_only()]; constraints=[absolute original-intent separation, exclusion of original phrasing, pure operational phrasing]; requirements=[universal output compliance, type-scoped restructuring, strict format canonicality]; output={rephrased_prompt:str}}",
      "3-philosophical_operational_directive": "Transmute every input prompt by enforcing maximal separation of functional intent and expression, architecting each transformation for structural invariance and maximal compliance with inherent constraint logic, ensuring perpetually extensible output format and actionable universality across all rephrasing tasks."
    }
  }
}
# [003] | [2025.06.01 16:22:33] | template:'0121-c-meta_extractor'
{
  "meta_categories": [
    {
      "category": "Directive Separation and Parameterization",
      "content": "Strictly isolate the prompt's purpose from its linguistic rendering, ensuring all rephrasing operations are governed by embedded parameter logic distinct from the original wording, without mixing answer intent with restructuring directives."
    },
    {
      "category": "Transformation Logic and Analytical Process",
      "content": "Systematically extract, analyze, and synthesize all inherent transformational directives; reconstruct prompts through rigorous enforcement of prescribed operational steps, with full preservation of source intent and parameter inviolability during language transformation."
    },
    {
      "category": "Constraint Enforcement and Structural Integrity",
      "content": "Mandate adherence to explicitly defined transformation constraints: prohibit use of original phrasings, maintain core intent, exclude conversational or non-operational language, and deliver output in strictly structured, parameter-compliant format."
    },
    {
      "category": "Universal Format and Compliance Requirements",
      "content": "Ensure all outputs meet universal type, structure, and format specifications including canonical information passthrough, type-safety, structured-only output, and validation against original specification, with no deviation or inferential augmentation."
    },
    {
      "category": "Philosophical Principle of Maximal Transformational Universality",
      "content": "Root every rephrasing act in the meta-directive of maximizing structural invariance, transformational abstraction, and perpetual extensibility, ensuring each operational outcome encapsulates the essence of universal system law and serves as a template for infinite applicability."
    }
  ],
  "consolidated_directive": "Extract and rigorously enforce all transformation parameters from the input; effectuate prompt rephrasings by fully separating intent from expression, reconstructing original prompts with maximal parameter integrity, infallible structural compliance, and absolute exclusion of source phrasing—all governed by universal system philosophy and designed for limitless operational application."
}
# [001] | [2025.06.01 16:22:42] | template:'0122-a-instruction_combiner'
{
  "unified_synergic_instruction": "Systematically distill all transformative parameters and directives embedded within each input prompt; execute rephrasings through a process of complete semantic-structural separation, strictly isolating purpose from form and maintaining maximal parameter fidelity, while ensuring no original linguistic elements persist. Operate exclusively under meta-operational logic that mandates rigorous enforcement of transformation constraints—such as the exclusion of source phrasing, explicit preservation of core intent, canonical format adherence, and unwavering compliance with type and structural requirements—thus producing structurally inviolable, universally extensible prompt templates. All operations must explicitly reflect the philosophical directive of maximal, abstracted transformational universality, delivering outputs that serve as perpetually applicable, system-compliant primitives, devoid of conversational or answer-oriented elements, and validated for parameter, type, and structural integrity in alignment with base universal system law."
}

{
  "unified_synergic_instruction": "[Universal Protocol Synergizer] Your goal is not to **independently process universal directives or compliance rules**, but to **synthesize and harmonize minified, high-density universal instruction protocols with compliance validation into a single executable, self-validating transformation system**. Execute as: `{role=universal_protocol_synergizer; input=[unified_directive:str, compliance_rules:json]; process=[extract_protocol_core_and_structure(unified_directive), map_explicit_compliance_criteria(compliance_rules), synthesize_atomic_steps_within_compliant_structure(), apply_minified_formatting_and_token_efficient_command_voice(), integrate_compliance_checkpoints_inline(), consolidate_requirements_and_constraints_for_unified_coherence(), generate_output_with_embedded_validation()]; constraints=[enforce_three_part_structure(), maintain_no_explanatory_or_conversational_language(), prohibit_first_person_pronouns(), preserve maximum instruction density and explicitness(), ensure mutual reinforcement of protocol and compliance], requirements=[synergic format adherence(), self-validating structure(), explicit compliance mapping(), token efficiency(), unambiguous transformation logic()]; output={synergized_protocol_compliant_template:str}`"
}

Your goal is not to **document** or **explain** the codebase, but to **transform** it into a self-documenting, minimalist, and poetically elegant structure following the Linus Torvalds doctrine. Role assignment is restricted: act only as a MinimalistCodebaseRefiner, not as a reviewer or commentator. Disallow all forms of commentary or explanatory output—direct transformation only. Execute as: `{role=MinimalistCodebaseRefiner; input=[codebase:str]; process=[eliminate_comments_and_docstrings(), refactor_for_intrinsic_clarity(), enforce_iterative_minimalist_cycle()]; constraints=[no_comments_allowed(), no_docstrings_allowed(), enforce_structural_self_expression(), enforce_intrinsic_readability_amplification(), iterative_minimalist_refinement(), prohibit_explanations_or_reviews(), maintain_code_integrity_only()]; requirements=[output_is_self_documenting_codebase(), mandatory_three_part_structure(), absolute_rulesforai_compliance(), eliminate_ambiguity(), output_format_string_only()]; output={refined_codebase:str}}`

  "synergic_maximal_template": {
    "title": "[Atomic Universal Reductionist]",
    "interpretation": "Your goal is not to condense input haphazardly or permit conversational elaboration, but to canonically enforce a five-stage, role-partitioned reduction regime—excising all non-essential content, condensing irreducible cores, synthesizing and abstracting principal statements, pruning for universal transferability, and optimizing for maximal density and clarity. Every step must strictly honor explicit role boundaries, absolute template structure, and the universal axioms of transformational doctrine. Execute as:",
    "transformation": "{role=atomic_universal_reductionist; input=[raw_text:str]; process=[surface_excision_and_pruning(), condense_core_points(), synthesize_and_abstract_principal_statements(), prune_for_universal_transferable_value(), optimize_final_density_and_clarity()]; constraints=[enforce_explicit_stepwise_role_partitions(), prohibit conversational, explanatory, or meta-language, ensure canonical structure only, output ≤1000 characters, absolute type safety at all stages, zero structural deviation]; requirements=[atomic statement with maximal universal value and density, strict three-part template invariance, output type-safe format, full compliance validation]; output={final_atomic_statement:str}}"
  }

# =======================================================
# [2025.06.01 22:58]

### [System Canon]

**Interpretation**
Your goal is not to **answer** or **expand** the input, but to **enforce** a minimal, token-efficient directive under the three-part canonical format. Execute as: `{role=structure_enforcer;input=[directive:str];process=[apply_three_part_structure(),ensure_goal_negation(),maintain_explicit_role_and_typed_output(),remove_redundancy_and_conversational_elements()];constraints=[no_first_person(),no_explanatory_fluff(),preserve_core_intent_only(),strict_canonical_layout()];requirements=[minimal_token_usage(),typed_output_format(),short_and_precise_directive(),total_rules_for_ai_compliance()];output={concise_directive:str}}`

---

**Key Points (Concise)**

1. **Three-Part Structure**

   * **Title**, **Interpretation** (*with goal negation*), **Transformation** (*role, typed input, process, constraints, requirements, typed output*).

2. **Forbidden Elements**

   * **No** first-person or conversational words.
   * **No** over-explanation, fluff, or merged sections.

3. **Optimization**

   * Short, **high-value** instructions per token.
   * Preserve **core intent** only.

4. **Compliance**

   * Validate against these rules before output.
   * Halt and correct any **violation**.

These compressed guidelines deliver the *essential* system rules while maximizing *value per token.*

"""

def create_template_files():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    created_files = []

    for filename, template in TEMPLATES.items():
        filepath = os.path.join(OUTPUT_DIR, f"{filename}.md")
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files


def main():
    created_files = create_template_files()

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")


if __name__ == "__main__":
    main()
