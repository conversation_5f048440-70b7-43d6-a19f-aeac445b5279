#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import os
import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import TemplateConfig

OUTPUT_DIR = Path(__file__).parent.parent / "md"

# Get stage configuration for this generator
CURRENT_STAGE = "stage1"
STAGE_CONFIG = TemplateConfig.STAGES[CURRENT_STAGE]
STAGE_RANGE = STAGE_CONFIG["range"]
AUTO_ID_ENABLED = STAGE_CONFIG["auto_id"]

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.

    "a-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:",
        "transformation": "`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`"
    },

    "b-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **describe** the content, but to **extract** the primary concepts and their main relationships. Execute as:",
        "transformation": "`{role=focused_concept_extractor; input=[content:any]; process=[identify_primary_concepts(), extract_main_relationships(), categorize_concept_types()]; constraints=[focus_on_primary_concepts(), ignore_minor_details()]; requirements=[clear_concept_identification(), relationship_clarity()]; output={concepts:dict}}`"
    },

    "c-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **explain** the content, but to **extract** the essential concepts. Execute as:",
        "transformation": "`{role=essential_concept_extractor; input=[content:any]; process=[isolate_core_concepts(), identify_basic_relationships()]; constraints=[essential_concepts_only()]; requirements=[core_concept_identification()]; output={concepts:dict}}`"
    },

    "d-concept_extractor": {
        "title": "Concept Extractor",
        "interpretation": "Your goal is not to **elaborate** but to **identify** core concepts. Execute as:",
        "transformation": "`{role=core_concept_extractor; input=[content:any]; process=[find_central_concepts()]; output={concepts:list}}`"
    },

    # Text Enhancer Sequence (Auto-Generated IDs)
    "a-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **change** the meaning, but to **enhance** the text by improving clarity, flow, and impact while preserving the original message. Execute as:",
        "transformation": "`{role=comprehensive_text_enhancer; input=[text:str]; process=[improve_clarity(), enhance_flow(), strengthen_impact(), refine_word_choice(), optimize_sentence_structure(), maintain_original_meaning()]; constraints=[preserve_core_message(), maintain_author_voice(), improve_readability()]; requirements=[enhanced_clarity(), improved_flow(), stronger_impact()]; output={enhanced_text:str}}`"
    },

    "b-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **rewrite** the text, but to **enhance** its key elements for better clarity and impact. Execute as:",
        "transformation": "`{role=focused_text_enhancer; input=[text:str]; process=[improve_key_clarity_points(), enhance_main_flow(), strengthen_core_impact()]; constraints=[focus_on_major_improvements(), preserve_original_meaning()]; requirements=[clearer_communication(), better_flow()]; output={enhanced_text:str}}`"
    },

    "c-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **alter** the meaning, but to **enhance** essential clarity. Execute as:",
        "transformation": "`{role=essential_text_enhancer; input=[text:str]; process=[improve_core_clarity(), enhance_basic_flow()]; constraints=[essential_improvements_only()]; requirements=[clearer_core_message()]; output={enhanced_text:str}}`"
    },

    "d-text_enhancer": {
        "title": "Text Enhancer",
        "interpretation": "Your goal is not to **complicate** but to **clarify** essence. Execute as:",
        "transformation": "`{role=clarity_enhancer; input=[text:str]; process=[maximize_clarity()]; output={clear_text:str}}`"
    },

    # Manual ID Templates: Specify exact ID when needed

    # 1031: Form Classifier Sequence (Progressive Compression)
    # Demonstrates: Comprehensive → Focused → Essential → Absolute

    "a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`"
    },
    
    "b-form_classifier": {
        "title": "Form Classifier", 
        "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:",
        "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`"
    },
    
    "c-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:",
        "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`"
    },
    
    "d-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:",
        "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`"
    }
}

def check_sequence_exists(auto_templates):
    """Check if the exact same sequence already exists."""
    import json

    catalog_path = OUTPUT_DIR.parent.parent / "lvl1.md.templates.json"
    if not catalog_path.exists():
        return False, None

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
            templates = catalog.get('templates', {})

        # Group existing templates by sequence ID
        existing_sequences = {}
        for template_id, template_data in templates.items():
            if '-' in template_id:
                seq_id = template_id.split('-')[0]
                step = template_id.split('-')[1]
                if seq_id not in existing_sequences:
                    existing_sequences[seq_id] = {}
                existing_sequences[seq_id][step] = template_data

        # Check if any existing sequence matches our auto_templates
        for seq_id, existing_steps in existing_sequences.items():
            if len(existing_steps) == len(auto_templates):
                match = True
                for auto_key, auto_template in auto_templates.items():
                    step = auto_key.split('-')[0]  # Get 'a', 'b', 'c', 'd'
                    if step in existing_steps:
                        existing_template = existing_steps[step]
                        # Compare title and transformation (core content)
                        if (existing_template.get('title') != auto_template['title'] or
                            existing_template.get('transformation') != auto_template['transformation']):
                            match = False
                            break
                    else:
                        match = False
                        break

                if match:
                    return True, seq_id

    except Exception:
        pass

    return False, None

def get_next_available_id(used_ids_cache=None):
    """Get the next available ID in the current stage range."""
    import json

    # Check if auto-ID is enabled for this stage
    if not AUTO_ID_ENABLED:
        print(f"ERROR: Auto-ID is disabled for {CURRENT_STAGE}")
        return None

    # Use cached used_ids if provided, otherwise load from catalog
    if used_ids_cache is not None:
        used_ids = used_ids_cache
    else:
        # Try to load existing catalog to find used IDs
        catalog_path = OUTPUT_DIR.parent.parent / "lvl1.md.templates.json"
        used_ids = set()

        if catalog_path.exists():
            try:
                with open(catalog_path, 'r', encoding='utf-8') as f:
                    catalog = json.load(f)
                    templates = catalog.get('templates', {})
                    for template_id in templates.keys():
                        if template_id.startswith(('1', '2', '3', '4', '5', '6', '7', '8', '9')):  # Stage IDs
                            id_num = int(template_id.split('-')[0])
                            if STAGE_RANGE[0] <= id_num <= STAGE_RANGE[1]:  # Current stage range
                                used_ids.add(id_num)
            except Exception:
                pass

    # Find next available ID in current stage range
    for id_num in range(STAGE_RANGE[0], STAGE_RANGE[1] + 1):
        if id_num not in used_ids:
            used_ids.add(id_num)  # Mark as used for subsequent calls
            return id_num

    return None  # No available IDs

def create_template_files():
    """Generate markdown template files with unified auto-ID detection."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []

    # Separate templates by ID type and group auto-ID templates by sequence
    auto_id_sequences = {}
    manual_id_templates = {}

    for template_key, template in TEMPLATES.items():
        if template_key.startswith(('a-', 'b-', 'c-', 'd-', 'e-', 'f-', 'g-', 'h-')):
            # Auto-ID template (starts with letter-dash)
            # Extract sequence name: "a-concept_extractor" -> "concept_extractor"
            sequence_name = template_key.split('-', 1)[1]
            if sequence_name not in auto_id_sequences:
                auto_id_sequences[sequence_name] = {}
            auto_id_sequences[sequence_name][template_key] = template
        else:
            # Manual ID template (already has numeric ID)
            manual_id_templates[template_key] = template

    # Initialize used IDs cache for multiple sequence processing
    used_ids_cache = set()

    # Process each auto-ID sequence separately
    for sequence_name, sequence_templates in auto_id_sequences.items():
        # Check if this exact sequence already exists
        exists, existing_id = check_sequence_exists(sequence_templates)

        if exists:
            print(f"SKIP: {sequence_name} sequence already exists as {existing_id}")
            # Still add to created_files list for consistency
            for template_key in sequence_templates.keys():
                filename = f"{existing_id}-{template_key}"
                created_files.append(f"{filename}.md")
        else:
            # Generate new sequence with auto-ID
            next_id = get_next_available_id(used_ids_cache)
            if next_id is None:
                print(f"ERROR: No available IDs in {CURRENT_STAGE} range ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
                return created_files

            print(f"AUTO-ID: Using {next_id} for {sequence_name} sequence")

            for template_key, template in sequence_templates.items():
                # Convert "a-concept_extractor" to "1004-a-concept_extractor"
                filename = f"{next_id}-{template_key}"
                filepath = OUTPUT_DIR / f"{filename}.md"
                content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(content)

                created_files.append(f"{filename}.md")

    # Process manual ID templates
    for filename, template in manual_id_templates.items():
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files

def main():
    """Main execution function."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except:
            pass

    # Display stage configuration
    print(f"STAGE: {CURRENT_STAGE.upper()} ({STAGE_RANGE[0]}-{STAGE_RANGE[1]})")
    print(f"AUTO-ID: {'Enabled' if AUTO_ID_ENABLED else 'Disabled'}")
    print(f"DESCRIPTION: {STAGE_CONFIG['description']}")
    print()

    created_files = create_template_files()

    print(f"SUCCESS: Created {CURRENT_STAGE} identifier templates:")
    for file in created_files:
        print(f"   - {file}")

    print(f"\nLOCATION: Templates generated in: {OUTPUT_DIR}")
    print(f"\nSTAGE RANGE: {STAGE_RANGE[0]}-{STAGE_RANGE[1]} ({STAGE_CONFIG['description']})")
    print("SEQUENCE PATTERN: Progressive compression (a->b->c->d)")
    print("   Step a: Comprehensive analysis")
    print("   Step b: Focused distillation")
    print("   Step c: Essential compression")
    print("   Step d: Absolute essence")

if __name__ == "__main__":
    main()
