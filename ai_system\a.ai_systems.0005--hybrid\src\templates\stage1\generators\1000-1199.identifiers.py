#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import os
from pathlib import Path

OUTPUT_DIR = Path(__file__).parent.parent / "md"

TEMPLATES = {
    # 1000: Content Analyzer Sequence (Auto-Generated IDs)
    # Demonstrates: Auto-ID generation for rapid prototyping

    "1000-a-content_analyzer": {
        "title": "Content Analyzer",
        "interpretation": "Your goal is not to **summarize** the content, but to **analyze** its structural composition, identifying key elements, patterns, and organizational framework. Execute as:",
        "transformation": "`{role=comprehensive_content_analyzer; input=[content:any]; process=[identify_structural_elements(), map_content_hierarchy(), detect_organizational_patterns(), analyze_information_density(), categorize_content_types(), extract_key_themes(), synthesize_structural_analysis()]; constraints=[focus_on_structure_not_meaning(), identify_patterns_not_content(), analyze_form_not_substance()]; requirements=[structural_pattern_identification(), organizational_framework_mapping(), content_type_categorization()]; output={structural_analysis:dict}}`"
    },

    "1000-b-content_analyzer": {
        "title": "Content Analyzer",
        "interpretation": "Your goal is not to **describe** the content, but to **analyze** its primary structural patterns and organizational elements. Execute as:",
        "transformation": "`{role=focused_content_analyzer; input=[content:any]; process=[identify_primary_structure(), extract_main_patterns(), categorize_organization_type()]; constraints=[focus_on_primary_elements(), ignore_secondary_details()]; requirements=[clear_structural_identification(), pattern_recognition()]; output={structural_analysis:dict}}`"
    },

    "1000-c-content_analyzer": {
        "title": "Content Analyzer",
        "interpretation": "Your goal is not to **explain** the content, but to **analyze** its essential structural elements. Execute as:",
        "transformation": "`{role=essential_content_analyzer; input=[content:any]; process=[isolate_core_structure(), identify_basic_patterns()]; constraints=[essential_elements_only()]; requirements=[basic_structural_identification()]; output={structural_analysis:dict}}`"
    },

    "1000-d-content_analyzer": {
        "title": "Content Analyzer",
        "interpretation": "Your goal is not to **elaborate** but to **identify** core structure. Execute as:",
        "transformation": "`{role=core_analyzer; input=[content:any]; process=[find_structural_essence()]; output={structure:str}}`"
    },

    # 1031: Form Classifier Sequence (Progressive Compression)
    # Demonstrates: Comprehensive → Focused → Essential → Absolute

    "1031-a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`"
    },
    
    "1031-b-form_classifier": {
        "title": "Form Classifier", 
        "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:",
        "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`"
    },
    
    "1031-c-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:",
        "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`"
    },
    
    "1031-d-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:",
        "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`"
    }
}

def create_template_files():
    """Generate markdown template files."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []

    for filename, template in TEMPLATES.items():
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
        
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
            
        created_files.append(f"{filename}.md")
    
    return created_files

def main():
    """Main execution function."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')

    created_files = create_template_files()

    print("SUCCESS: Created Stage 1 identifier templates:")
    for file in created_files:
        print(f"   - {file}")

    print(f"\nLOCATION: Templates generated in: {OUTPUT_DIR}")
    print("\nSEQUENCE: Demonstrates progressive compression:")
    print("   1031-a: Comprehensive (15+ process steps)")
    print("   1031-b: Focused (3 process steps)")
    print("   1031-c: Essential (3 process steps)")
    print("   1031-d: Absolute (2 process steps)")

if __name__ == "__main__":
    main()
