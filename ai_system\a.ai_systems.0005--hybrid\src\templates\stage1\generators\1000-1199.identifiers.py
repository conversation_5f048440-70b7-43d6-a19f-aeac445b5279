#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import os
from pathlib import Path

OUTPUT_DIR = Path(__file__).parent.parent / "md"

# Auto-ID Templates (Stage 1 only)
# System will automatically assign next available ID in 1000-1999 range
AUTO_ID_TEMPLATES = {
    # Text Simplifier Sequence (Auto-Generated IDs)
    # Demonstrates: Progressive simplification for accessibility

    "a-text_simplifier": {
        "title": "Text Simplifier",
        "interpretation": "Your goal is not to **summarize** the text, but to **simplify** its language and structure while preserving all key information and meaning. Execute as:",
        "transformation": "`{role=comprehensive_text_simplifier; input=[text:str]; process=[identify_complex_language(), break_down_long_sentences(), replace_difficult_vocabulary(), clarify_abstract_concepts(), maintain_original_meaning(), ensure_accessibility()]; constraints=[preserve_all_information(), maintain_logical_flow(), use_simple_language()]; requirements=[clear_communication(), accessible_vocabulary(), simplified_structure()]; output={simplified_text:str}}`"
    },

    "b-text_simplifier": {
        "title": "Text Simplifier",
        "interpretation": "Your goal is not to **rewrite** the text, but to **simplify** its most complex elements while keeping the core message clear. Execute as:",
        "transformation": "`{role=focused_text_simplifier; input=[text:str]; process=[identify_main_complexity_barriers(), simplify_key_concepts(), clarify_essential_points()]; constraints=[focus_on_major_simplifications(), preserve_core_meaning()]; requirements=[improved_clarity(), accessible_language()]; output={simplified_text:str}}`"
    },

    "c-text_simplifier": {
        "title": "Text Simplifier",
        "interpretation": "Your goal is not to **change** the meaning, but to **simplify** the essential message. Execute as:",
        "transformation": "`{role=essential_text_simplifier; input=[text:str]; process=[extract_core_message(), use_basic_language()]; constraints=[essential_simplification_only()]; requirements=[clear_core_message()]; output={simplified_text:str}}`"
    },

    "d-text_simplifier": {
        "title": "Text Simplifier",
        "interpretation": "Your goal is not to **expand** but to **distill** to simplest form. Execute as:",
        "transformation": "`{role=minimal_simplifier; input=[text:str]; process=[find_simplest_expression()]; output={simple_text:str}}`"
    },
}

# Manual ID Templates (for specific IDs)
TEMPLATES = {

    # 1031: Form Classifier Sequence (Progressive Compression)
    # Demonstrates: Comprehensive → Focused → Essential → Absolute

    "a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`"
    },
    
    "b-form_classifier": {
        "title": "Form Classifier", 
        "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:",
        "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`"
    },
    
    "c-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:",
        "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`"
    },
    
    "d-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:",
        "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`"
    }
}

def check_sequence_exists(auto_templates):
    """Check if the exact same sequence already exists."""
    import json

    catalog_path = OUTPUT_DIR.parent.parent / "lvl1.md.templates.json"
    if not catalog_path.exists():
        return False, None

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog = json.load(f)
            templates = catalog.get('templates', {})

        # Group existing templates by sequence ID
        existing_sequences = {}
        for template_id, template_data in templates.items():
            if '-' in template_id:
                seq_id = template_id.split('-')[0]
                step = template_id.split('-')[1]
                if seq_id not in existing_sequences:
                    existing_sequences[seq_id] = {}
                existing_sequences[seq_id][step] = template_data

        # Check if any existing sequence matches our auto_templates
        for seq_id, existing_steps in existing_sequences.items():
            if len(existing_steps) == len(auto_templates):
                match = True
                for auto_key, auto_template in auto_templates.items():
                    step = auto_key.split('-')[0]  # Get 'a', 'b', 'c', 'd'
                    if step in existing_steps:
                        existing_template = existing_steps[step]
                        # Compare title and transformation (core content)
                        if (existing_template.get('title') != auto_template['title'] or
                            existing_template.get('transformation') != auto_template['transformation']):
                            match = False
                            break
                    else:
                        match = False
                        break

                if match:
                    return True, seq_id

    except Exception:
        pass

    return False, None

def get_next_available_id():
    """Get the next available ID in the 1000-1999 range."""
    import json

    # Try to load existing catalog to find used IDs
    catalog_path = OUTPUT_DIR.parent.parent / "lvl1.md.templates.json"
    used_ids = set()

    if catalog_path.exists():
        try:
            with open(catalog_path, 'r', encoding='utf-8') as f:
                catalog = json.load(f)
                templates = catalog.get('templates', {})
                for template_id in templates.keys():
                    if template_id.startswith(('1', '2', '3')):  # Stage IDs
                        id_num = int(template_id.split('-')[0])
                        if 1000 <= id_num <= 1999:  # Stage 1 range
                            used_ids.add(id_num)
        except Exception:
            pass

    # Find next available ID in stage1 range
    for id_num in range(1000, 2000):
        if id_num not in used_ids:
            return id_num

    return None  # No available IDs

def create_template_files():
    """Generate markdown template files with auto-ID generation."""
    OUTPUT_DIR.mkdir(exist_ok=True)
    created_files = []

    # Process auto-ID templates first
    if AUTO_ID_TEMPLATES:
        # Check if this exact sequence already exists
        exists, existing_id = check_sequence_exists(AUTO_ID_TEMPLATES)

        if exists:
            print(f"SKIP: Sequence already exists as {existing_id} (no changes needed)")
            # Still add to created_files list for consistency
            for template_key in AUTO_ID_TEMPLATES.keys():
                filename = f"{existing_id}-{template_key}"
                created_files.append(f"{filename}.md")
        else:
            # Generate new sequence with auto-ID
            next_id = get_next_available_id()
            if next_id is None:
                print("ERROR: No available IDs in Stage1 range (1000-1999)")
                return created_files

            print(f"AUTO-ID: Using {next_id} for new sequence")

            for template_key, template in AUTO_ID_TEMPLATES.items():
                # Convert "a-template_name" to "1000-a-template_name"
                filename = f"{next_id}-{template_key}"
                filepath = OUTPUT_DIR / f"{filename}.md"
                content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(content)

                created_files.append(f"{filename}.md")

    # Process manual ID templates
    for filename, template in TEMPLATES.items():
        filepath = OUTPUT_DIR / f"{filename}.md"
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files

def main():
    """Main execution function."""
    import sys

    # Ensure UTF-8 output encoding
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')

    created_files = create_template_files()

    print("SUCCESS: Created Stage 1 identifier templates:")
    for file in created_files:
        print(f"   - {file}")

    print(f"\nLOCATION: Templates generated in: {OUTPUT_DIR}")
    print("\nSEQUENCE: Demonstrates progressive compression:")
    print("   1031-a: Comprehensive (15+ process steps)")
    print("   1031-b: Focused (3 process steps)")
    print("   1031-c: Essential (3 process steps)")
    print("   1031-d: Absolute (2 process steps)")

if __name__ == "__main__":
    main()
