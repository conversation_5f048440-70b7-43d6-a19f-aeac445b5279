  {
  "initial_prompt": "The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.",
  "sequence_id": "1001",
  "results": [
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "a",
      "title": "Form Classifier",
      "input": "The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"what_it_is\":\"informal written communication; personal opinion or reflection; narrative statement\"}",
        }
      }
    },
      {
      "instruction": "# Text Enhancer\n\nYour goal is not to **change** the meaning, but to **enhance** the text by improving clarity, flow, and impact while preserving the original message. Execute as:\n\n`{role=comprehensive_text_enhancer; input=[text:str]; process=[improve_clarity(), enhance_flow(), strengthen_impact(), refine_word_choice(), optimize_sentence_structure(), maintain_original_meaning()]; constraints=[preserve_core_message(), maintain_author_voice(), improve_readability()]; requirements=[enhanced_clarity(), improved_flow(), stronger_impact()]; output={enhanced_text:str}}`",
      "step": "a",
      "title": "Text Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"what_it_is\": \"informal written communication; personal opinion or reflection; narrative statement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_text\": \"The meeting went reasonably well, and we discussed several topics that could be important for the project. However, I'm still uncertain about what the next steps should be.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:\n\n`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"enhanced_text\": \"The meeting went reasonably well, and we discussed several topics that could be important for the project. However, I'm still uncertain about what the next steps should be.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"informal personal reflection\"\n}",
        }
      }
    },
      {
      "instruction": "# Text Enhancer\n\nYour goal is not to **rewrite** the text, but to **enhance** its key elements for better clarity and impact. Execute as:\n\n`{role=focused_text_enhancer; input=[text:str]; process=[improve_key_clarity_points(), enhance_main_flow(), strengthen_core_impact()]; constraints=[focus_on_major_improvements(), preserve_original_meaning()]; requirements=[clearer_communication(), better_flow()]; output={enhanced_text:str}}`",
      "step": "b",
      "title": "Text Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"what_it_is\": \"informal personal reflection\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_text\": \"The meeting went reasonably well. We discussed several topics that could be significant for the project, though I’m still unclear about what the next steps should be.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:\n\n`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
      "step": "c",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"enhanced_text\": \"The meeting went reasonably well. We discussed several topics that could be significant for the project, though I’m still unclear about what the next steps should be.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"what_it_is\":\"meeting_feedback_statement\"}",
        }
      }
    },
      {
      "instruction": "# Text Enhancer\n\nYour goal is not to **alter** the meaning, but to **enhance** essential clarity. Execute as:\n\n`{role=essential_text_enhancer; input=[text:str]; process=[improve_core_clarity(), enhance_basic_flow()]; constraints=[essential_improvements_only()]; requirements=[clearer_core_message()]; output={enhanced_text:str}}`",
      "step": "c",
      "title": "Text Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"what_it_is\": \"meeting_feedback_statement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_text\": \"The meeting was satisfactory, and we discussed topics that could be important for the project. However, I am unclear about what the next steps should be.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:\n\n`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
      "step": "d",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"enhanced_text\": \"The meeting was satisfactory, and we discussed topics that could be important for the project. However, I am unclear about what the next steps should be.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"what_it_is\":\"meeting summary\"}",
        }
      }
    },
      {
      "instruction": "# Text Enhancer\n\nYour goal is not to **complicate** but to **clarify** essence. Execute as:\n\n`{role=clarity_enhancer; input=[text:str]; process=[maximize_clarity()]; output={clear_text:str}}`",
      "step": "d",
      "title": "Text Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"The meeting was okay and we talked about some stuff that might be important for the project but I'm not really sure what the next steps are.\\\"```'\\n\\n\",\n  \"what_it_is\": \"meeting summary\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"clear_text\": \"The meeting covered topics that could be important for the project, but the next steps were not clearly defined.\"\n}",
        }
      }
    }