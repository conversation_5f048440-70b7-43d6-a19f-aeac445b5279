
# Instruction Sequence Executor

This tool allows you to execute a single user prompt through a sequence of different system instructions using multiple LLM models. It's particularly useful for getting diverse perspectives on a single question or problem. It leverages the `litellm` library to potentially use multiple different Large Language Models (LLMs) for each step in the sequence, allowing for diverse outputs or step-by-step refinement based on the same initial user input.

## Features

- Execute a user prompt through multiple system instructions
- Run each instruction on multiple LLM models
- Load instruction sequences from template files
- Track costs for each execution
- Save results in structured JSON format

## Key Components and Functionality

1.  `lvl1_sequence_executor.py`:
    - Core Logic: This is the main script that orchestrates the execution flow.
    - LLM Interaction: Uses `litellm` to communicate with various LLM APIs, handling model calls, retries, and timeouts. It includes a `MODEL_MAPPING` to translate user-friendly names (like `gpt-4o-openai`) to API-specific model identifiers (`gpt-4o`).
    - Instruction Sequences: Can load sequences in two ways:
        - Catalog Mode: Discovers and parses individual `.md` template files from `src/templates/lvl1` using `generate_catalog`. The filenames (`NNNN-L-description.md`) define the sequence ID (`NNNN`) and step order (`L`). It executes each instruction in the chosen sequence using the *original* user prompt.
        - Text Mode: Loads sequences from `.txt` files in `src/templates`, where instructions are separated by `---`. It executes each instruction from the file sequentially using the *original* user prompt.
    - Streaming Output: Executes LLM calls asynchronously (`asyncio`) and streams the results incrementally to a structured JSON output file.
    - Cost Tracking: Integrates with `litellm`'s cost calculation features to track and report the estimated cost of API calls.
    - Structured Output: Uses Pydantic models (`ModelResponse`, `InstructionResult`, `ExecutionResults`) to define the structure of the final JSON output, which includes the user prompt, sequence name, results per instruction/model, and total cost.
    - Command-Line Interface: Uses `argparse` to accept user input for the prompt, the sequence to run, the models to use, the output file path, and whether to use text-based sequences.

2.  `src/templates/lvl1/*.md`:
    - Individual Instructions: This directory contains a large collection of Markdown files, each acting as a specific system prompt or template for an LLM.
    - Sequences: They are organized into sequences by the numeric prefix in their filenames (e.g., all files starting with `0002-` form sequence "0002").
    - Purpose: The templates cover a wide range of sophisticated tasks, including prompt refinement, text analysis, essence extraction, code refactoring, structural analysis, context summarization, and complex logical transformations. Many use a specific structured format (`[Title] Description {role=...; input=...; process=...; output=...}`) to define the LLM's task. Sequences 0048-0055 specifically address codebase understanding and cross-platform setup/execution concerns.

3.  `src/templates/breadcrumbs_catalog_generator.py`:
    - Utility Script: Scans the `src/templates/lvl1` directory.
    - Parsing: Extracts metadata (ID, title, role, sequence info) from the `.md` template files based on filename patterns and content.
    - Catalog Generation: Designed to output a `breadcrumbs_catalog.json` file summarizing all templates and organizing them into sequences (though the main executor seems to perform this cataloging in memory).

4.  Setup & Configuration:
    - `py_venv_init.bat`: A Windows batch script to automate Python virtual environment creation, activation, and dependency installation/update from `requirements.txt`.
    - `requirements.txt`: Lists necessary Python packages, notably `litellm` and `pydantic`.
    - `g_system_prompting.sublime-project`: A Sublime Text project configuration file defining folder structure, exclusions, Python environment settings (interpreter, linter, formatter), and a build system for running Python files within the project's `venv`. It also references an external `chainlite` repository path.
    - `README.md`: Provides a concise overview of the project's purpose and core features.

In essence, it provides a powerful and flexible tool for experimenting with complex, multi-step prompt engineering and LLM interactions. It allows users to chain pre-defined, sophisticated instructions together and apply them to a user prompt using various LLMs, while managing execution, tracking costs, and saving structured results.

---

# Template Structure Guide

This guide documents the standardized structure for creating *generalized* (and maximally enhanced) llm-optimized `system_message` templates.

## Rules

Each template is stored as a markdown (.md) file and follows this standardized three-part structure ("[Title] Interpretation text `{transformation}`"):
1. __[TITLE]__: Enclosed in square brackets `[]`, defining the template's purpose.
   - Should be concise, descriptive, and follow title case formatting
   - Examples: `[Instruction Converter]`, `[Essence Distillation]`
2. __[INTERPRETATION]__: Plain text immediately following the title that describes what the template does.
   - How to activate the synthesized essence as an executable system capable of autonomous recursion and dynamic adaptability.
   - Should clearly explain the template's function in natural language
   - Can include formatting like **bold**, *italic*, or other markdown elements
   - Provides context for human readers to understand the template's purpose
3. __[TRANSFORMATION]__: JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.
   - How to scaffold the previously infused value into a reusable, meta-aware instruction engine that can execute, learn, and self-evolve.
   - Contains the structured representation of the transformation process
   - Uses a consistent semi-colon separated key-value format

Instruction template example (to demonstrate the "syntax" and fundamental/inherent concepts):
```
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`
```

The transformation component must follow this standardized format:
```
{role=<role_name>; input=[<input_params>]; process=[<process_steps>]; constraints=[<constraints>]; requirements=[<requirements>]; output={<output_format>}}
```

... which concist of these components:
```
> [ROLE]: Defines the functional role of the template
   - Example: `role=essence_distiller`
> [INPUT]: Specifies the expected input format and parameters
   - Uses array syntax with descriptive parameter names
   - Example: `input=[original:any]`
> [PROCESS]: Lists the processing steps to be executed in order
   - Uses array syntax with function-like step definitions
   - Can include parameters within step definitions
   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`
> [constraints (optional)]: Specifies limitations or boundaries for the transformation
   - Uses array syntax with directive-like constraints
   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`
> [requirements (optional)]: Defines mandatory aspects of the transformation
   - Uses array syntax with imperative requirements
   - Example: `requirements=[remove_self_references(), use_command_voice()]`
> [OUTPUT]: Specifies the expected output format
   - Uses object syntax with typed output parameters
   - Example: `output={distilled_essence:any}`
```

Guidelines (for effective instructions):
- Clarity: Each component should clearly communicate its purpose and functionality
- Specificity: Be specific about input/output formats and processing steps
- Modularity: Design templates to perform discrete, focused transformations
- Composability: For sequence templates, ensure outputs from one step can serve as inputs to the next
- Consistency: Follow the standardized structure and naming conventions exactly
- Self-Documentation: The interpretation text should provide sufficient context for understanding
- Functional Completeness: Ensure the transformation logic includes all necessary components
- Self-Enhancing Adaptive Output: The final output is not just adaptable, but recursively self-enhancing and future-proofed for any context or system.
- Modular Catalytic Sequences: Each step is maximally generalized, modular, and exponentially catalytic when executed in sequence.
- Essence Encoding: Each “interpretation” is essence-first and each “transformation” encodes the process and output contract, allowing direct LLM or process orchestration.







## Template Naming Convention
Templates follow a consistent naming convention that indicates their sequence and position:
```
<sequence_id>-<step>-<descriptive_name>.md
```

### Naming Components
> [SEQUENCE_ID]: A numeric identifier (e.g., 0001, 0002) that groups related templates
   - Four-digit format with leading zeros
   - Unique per sequence
> [step (optional)]: A lowercase letter (a, b, c, d, e) that indicates the position within a sequence
   - Only used for templates that are part of multi-step sequences
   - Follows alphabetical order to determine execution sequence
> [DESCRIPTIVE-NAME]: A hyphenated name that describes the template's purpose
   - All lowercase
   - Words separated by hyphens
   - Should be concise but descriptive

### Examples
- Single template: `0001-instructionconverter.md`
- Sequence templates:
  - `0002-a-essence-distillation.md`
  - `0002-b-exposing-coherence.md`
  - `0002-c-precision-enhancement.md`
  - `0002-d-structured-transformation.md`
  - `0002-e-achieving-self-explanation.md`

## Template Examples

### Example 1: Simple Template
```markdown
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`
```

### Example 2: Sequence Step Template
```markdown
[Essence Distillation] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
```

## Creating New Templates
To create a new template:
1. Determine if it should be a standalone template or part of a sequence
2. Assign an appropriate sequence_id (and step letter if part of a sequence)
3. Create a descriptive name using hyphenated lowercase words
4. Define the title that clearly indicates the template's purpose
5. Write the interpretation text that explains what the template does
6. Structure the transformation logic using the standardized format
7. Save the file with the proper naming convention
8. Run the catalog generator to include the new template in the JSON catalog

## Integration with Catalog Generator
The catalog generator extracts metadata from template files based on specific patterns. It is essential to follow the template structure exactly to ensure proper extraction.
The primary pattern used for extraction is:
```
\[(.*?)\]\s*(.*?)\s*(`\{.*?\}`)
```
This pattern extracts:
1. The title from within square brackets
2. The interpretation text following the title
3. The transformation within backticks and curly braces
Any deviation from this structure may result in improper extraction by the catalog generator.

---
